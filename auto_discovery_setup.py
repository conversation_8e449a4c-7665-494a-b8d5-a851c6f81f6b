#!/usr/bin/env python3
"""
Configuration automatique de la découverte réseau
"""

import subprocess
import ipaddress
from app import app, db, DiscoveryRule, HostGroup, Template

def get_network_info():
    """Détecte automatiquement le réseau local"""
    try:
        # Obtenir l'interface par défaut
        result = subprocess.run(['ip', 'route', 'show', 'default'], 
                              capture_output=True, text=True)
        default_route = result.stdout.strip()
        interface = default_route.split('dev ')[1].split()[0]
        
        # Obtenir l'adresse IP et le masque
        result = subprocess.run(['ip', 'addr', 'show', interface], 
                              capture_output=True, text=True)
        
        for line in result.stdout.split('\n'):
            if 'inet ' in line and 'scope global' in line:
                ip_with_mask = line.strip().split()[1]
                network = ipaddress.ip_network(ip_with_mask, strict=False)
                return str(network), str(network.network_address), interface
        
        return None, None, None
    except Exception as e:
        print(f"Erreur lors de la détection réseau: {e}")
        return None, None, None

def create_discovery_rules():
    """Crée automatiquement les règles de découverte"""
    with app.app_context():
        network_cidr, network_addr, interface = get_network_info()
        
        if not network_cidr:
            print("❌ Impossible de détecter le réseau automatiquement")
            return False
        
        print(f"🔍 Réseau détecté: {network_cidr}")
        print(f"📡 Interface: {interface}")
        
        # Vérifier si la règle existe déjà
        existing_rule = DiscoveryRule.query.filter_by(
            name=f"Auto-Discovery {network_cidr}"
        ).first()
        
        if existing_rule:
            print(f"✅ Règle existante trouvée: {existing_rule.name}")
            print(f"   Réseau: {existing_rule.network_range}")
            print(f"   Statut: {'Activée' if existing_rule.enabled else 'Désactivée'}")
            
            # Activer la règle si elle est désactivée
            if not existing_rule.enabled:
                existing_rule.enabled = True
                db.session.commit()
                print("🔄 Règle activée automatiquement")
            
            return True
        
        # Obtenir les groupes et templates par défaut
        auto_group = HostGroup.query.filter_by(name='Découverte Auto').first()
        auto_template = Template.query.filter_by(name='Auto Discovery').first()
        
        if not auto_group:
            print("❌ Groupe 'Découverte Auto' non trouvé")
            return False
        
        if not auto_template:
            print("❌ Template 'Auto Discovery' non trouvé")
            return False
        
        # Créer la règle de découverte
        new_rule = DiscoveryRule(
            name=f"Auto-Discovery {network_cidr}",
            network_range=network_cidr,
            discovery_type='network',
            check_interval=1800,  # 30 minutes
            enabled=True,
            auto_add_hosts=True,
            default_host_group_id=auto_group.id,
            default_template_id=auto_template.id,
            snmp_community='public'
        )
        
        db.session.add(new_rule)
        db.session.commit()
        
        print(f"✅ Règle de découverte créée:")
        print(f"   Nom: {new_rule.name}")
        print(f"   Réseau: {new_rule.network_range}")
        print(f"   Intervalle: {new_rule.check_interval//60} minutes")
        print(f"   Auto-ajout: {'Oui' if new_rule.auto_add_hosts else 'Non'}")
        print(f"   Groupe: {auto_group.name}")
        print(f"   Template: {auto_template.name}")
        
        return True

def test_discovery():
    """Test la découverte sur le réseau détecté"""
    from app import run_discovery_rule
    
    with app.app_context():
        network_cidr, _, _ = get_network_info()
        if not network_cidr:
            return False
        
        # Trouver la règle correspondante
        rule = DiscoveryRule.query.filter_by(
            name=f"Auto-Discovery {network_cidr}"
        ).first()
        
        if not rule:
            print("❌ Aucune règle de découverte trouvée")
            return False
        
        print(f"🧪 Test de découverte sur {network_cidr}...")
        print("⏳ Cela peut prendre quelques minutes...")
        
        # Exécuter la découverte
        result = run_discovery_rule(rule.id)
        
        if result['success']:
            print(f"✅ {result['message']}")
            print(f"   Hôtes découverts: {result.get('discovered_count', 0)}")
            print(f"   Nouveaux hôtes: {result.get('new_hosts_count', 0)}")
            
            # Afficher les hôtes découverts
            from app import DiscoveredHost
            hosts = DiscoveredHost.query.filter_by(discovery_rule_id=rule.id).all()
            
            if hosts:
                print(f"\n📋 Hôtes découverts:")
                for host in hosts[-5:]:  # Derniers 5
                    print(f"   • {host.ip_address} - {host.hostname or 'N/A'} ({host.device_type})")
            
            return True
        else:
            print(f"❌ {result['message']}")
            return False

def main():
    """Menu principal"""
    print("🔍 CONFIGURATION AUTOMATIQUE DE LA DÉCOUVERTE")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Détecter le réseau et créer la règle")
        print("2. Tester la découverte")
        print("3. Afficher les informations réseau")
        print("4. Quitter")
        
        choice = input("\nVotre choix (1-4): ").strip()
        
        if choice == '1':
            print("\n🔧 Configuration automatique...")
            if create_discovery_rules():
                print("\n✅ Configuration terminée avec succès!")
                print("Vous pouvez maintenant aller dans l'interface web:")
                print("→ Découverte Auto → Dashboard")
            else:
                print("\n❌ Erreur lors de la configuration")
                
        elif choice == '2':
            print("\n🧪 Test de la découverte...")
            test_discovery()
            
        elif choice == '3':
            network_cidr, network_addr, interface = get_network_info()
            if network_cidr:
                print(f"\n📡 Informations réseau:")
                print(f"   Réseau: {network_cidr}")
                print(f"   Adresse réseau: {network_addr}")
                print(f"   Interface: {interface}")
            else:
                print("\n❌ Impossible de détecter le réseau")
                
        elif choice == '4':
            print("\nAu revoir!")
            break
        else:
            print("\n❌ Choix invalide!")

if __name__ == "__main__":
    main()
