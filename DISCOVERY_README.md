# 🔍 Système de Découverte Automatique

## Vue d'ensemble

Le système de découverte automatique permet de détecter automatiquement les équipements réseau et de les ajouter à la surveillance, exactement comme dans Zabbix.

## 🎯 Fonctionnalités

### ✅ Implémenté
- **Règles de découverte configurables** : Définir des plages réseau à scanner
- **Détection automatique d'équipements** : Via PING et SNMP
- **Classification automatique** : Routeurs, switches, serveurs, postes de travail
- **Détection de services** : Ports ouverts et services disponibles
- **Auto-ajout à la surveillance** : Ajout automatique des nouveaux hôtes
- **Événements de découverte** : Historique complet des découvertes
- **Interface web complète** : Gestion via l'interface utilisateur

### 🔗 Relation avec les Alertes

La découverte est **directement liée** aux alertes :

1. **Nouveaux hôtes découverts** → Alerte informative générée
2. **Hôtes ajoutés automatiquement** → Surveillance active + règles d'alerte appliquées
3. **Hôtes perdus** → Alertes de perte de connectivité
4. **Services détectés** → Surveillance des services critiques

## 📋 Comment utiliser

### 1. Accéder à la Découverte
- Connectez-vous à l'application
- Cliquez sur **"Découverte Auto"** dans le menu de gauche
- Vous arrivez sur le dashboard de découverte

### 2. Créer une Règle de Découverte
1. Cliquez sur **"Gérer les Règles"**
2. Remplissez le formulaire :
   - **Nom** : Ex: "Réseau Bureau"
   - **Plage réseau** : Ex: `***********/24`
   - **Intervalle** : Fréquence de scan (1h par défaut)
   - **Auto-ajout** : Cocher pour ajouter automatiquement les nouveaux hôtes
   - **Groupe par défaut** : Groupe où placer les nouveaux hôtes
   - **Template par défaut** : Template à appliquer aux nouveaux hôtes

### 3. Exécuter la Découverte
- **Manuelle** : Bouton "Exécuter maintenant" sur une règle
- **Automatique** : Selon l'intervalle configuré
- **Toutes les règles** : Bouton "Lancer Toutes les Découvertes"

### 4. Gérer les Hôtes Découverts
1. Aller dans **"Hôtes Découverts"**
2. Voir tous les équipements détectés
3. Actions possibles :
   - **Ajouter** : Ajouter manuellement à la surveillance
   - **Ignorer** : Marquer comme ignoré
   - **Filtrer** : Par statut, type, etc.

## 🔧 Configuration Technique

### Types de Découverte
- **Network** : Scan PING + SNMP sur une plage IP
- **SNMP** : Découverte via SNMP uniquement
- **Agent** : Via agents Zabbix (futur)

### Détection Automatique
```python
# Types d'équipements détectés automatiquement
device_types = {
    'router': ['router', 'cisco'],
    'switch': ['switch'],
    'server': ['linux', 'ubuntu', 'centos', 'server'],
    'workstation': ['windows']
}
```

### Services Détectés
- SSH (22), Telnet (23), SMTP (25), DNS (53)
- HTTP (80), HTTPS (443), RDP (3389)
- PostgreSQL (5432), MySQL (3306)
- Et plus...

## 📊 Base de Données

### Tables Principales
- **`discovery_rule`** : Règles de découverte
- **`discovered_host`** : Hôtes découverts
- **`discovery_event`** : Événements de découverte

### Statuts des Hôtes
- **`discovered`** : Nouvellement découvert, en attente
- **`added`** : Ajouté à la surveillance
- **`ignored`** : Ignoré par l'utilisateur

## 🧪 Tests

### Script de Test
```bash
# Tester le système
python test_discovery.py

# Tester une règle spécifique
python test_discovery.py test

# Créer une règle de test
python test_discovery.py create

# Voir les hôtes découverts
python test_discovery.py hosts
```

### Test Manuel
1. Créer une règle pour votre réseau local
2. L'exécuter manuellement
3. Vérifier les résultats dans "Hôtes Découverts"
4. Ajouter quelques hôtes à la surveillance
5. Vérifier les alertes générées

## 🚀 Exemple d'Utilisation

### Scénario : Bureau avec réseau ***********/24

1. **Créer la règle** :
   - Nom: "Réseau Bureau"
   - Réseau: `***********/24`
   - Intervalle: 1 heure
   - Auto-ajout: Activé
   - Groupe: "Postes de travail"

2. **Résultat attendu** :
   - Détection automatique de tous les équipements
   - Classification : serveurs, postes, imprimantes, etc.
   - Ajout automatique à la surveillance
   - Alertes générées pour les nouveaux équipements

3. **Surveillance continue** :
   - Scan automatique toutes les heures
   - Détection des nouveaux équipements
   - Alertes si des équipements disparaissent

## 🔄 Intégration avec Zabbix

### Similitudes avec Zabbix
- **Discovery Rules** : Même concept
- **Auto-registration** : Ajout automatique
- **LLD (Low-Level Discovery)** : Détection de services
- **Actions** : Réactions automatiques aux découvertes

### Avantages de notre implémentation
- Interface plus moderne et intuitive
- Configuration simplifiée
- Intégration native avec le système d'alertes
- Détection de services intégrée

## 🛠️ Dépannage

### Problèmes Courants

1. **Aucun hôte découvert** :
   - Vérifier la plage réseau
   - Tester la connectivité PING
   - Vérifier la configuration SNMP

2. **SNMP ne fonctionne pas** :
   - Vérifier la communauté SNMP
   - Tester avec `snmpget` en ligne de commande
   - Vérifier les firewalls

3. **Hôtes non ajoutés automatiquement** :
   - Vérifier que "Auto-ajout" est activé
   - Vérifier les groupes et templates par défaut
   - Consulter les logs d'erreur

### Logs de Debug
```bash
# Voir les logs de découverte
tail -f app.log | grep "DISCOVERY"

# Activer le debug
export FLASK_DEBUG=1
python app.py
```

## 🔮 Évolutions Futures

- **Discovery via agents** : Support des agents Zabbix
- **Templates de découverte** : Templates spécialisés par type d'équipement
- **Découverte de services** : Détection automatique des services critiques
- **Corrélation d'événements** : Liens entre découvertes et incidents
- **API REST** : Intégration avec des systèmes externes

---

**Note** : Ce système de découverte est conçu pour être aussi proche que possible de Zabbix tout en étant plus simple à utiliser et à configurer.
