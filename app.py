from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from functools import wraps
import subprocess
import platform
import ipaddress
from datetime import datetime, timedelta, timezone
from pysnmp.hlapi import *
import concurrent.futures
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///monitoring.db?timeout=20'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_timeout': 20,
    'pool_recycle': -1,
    'pool_pre_ping': True
}
app.secret_key = "secret_key_super_secrète"  # Change-la en vraie clé secrète
db = SQLAlchemy(app)

# Initialisation de Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
login_manager.login_message_category = 'warning'

# Host Groups (like Zabbix)
class HostGroup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

# Templates (like Zabbix)
class Template(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

# Enhanced Machine/Host model
class Machine(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    cpu_load = db.Column(db.Integer, nullable=True)
    memory_usage = db.Column(db.Integer, nullable=True)  # Memory usage percentage
    disk_usage = db.Column(db.Integer, nullable=True)    # Disk usage percentage
    uptime = db.Column(db.Integer, nullable=True)        # Uptime in seconds
    description = db.Column(db.String(500), nullable=True)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    snmp_community = db.Column(db.String(100), default='public')
    snmp_version = db.Column(db.String(10), default='2c')
    monitoring_enabled = db.Column(db.Boolean, default=True)

    # Relationships
    host_group = db.relationship('HostGroup', backref='machines')
    template = db.relationship('Template', backref='machines')

# Enhanced Alert model with severity levels
class Alert(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    acknowledged = db.Column(db.Boolean, default=False)
    ack_timestamp = db.Column(db.DateTime, nullable=True)
    ack_user = db.Column(db.String(100), nullable=True)
    resolved = db.Column(db.Boolean, default=False)
    resolved_timestamp = db.Column(db.DateTime, nullable=True)

# Items (monitoring metrics like Zabbix)
class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    key = db.Column(db.String(200), nullable=False)  # e.g., 'system.cpu.load', 'vm.memory.size'
    oid = db.Column(db.String(200), nullable=True)   # SNMP OID
    value_type = db.Column(db.String(20), default='numeric')  # numeric, text, log
    units = db.Column(db.String(50), nullable=True)  # %, B, bps, etc.
    description = db.Column(db.String(500), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    active = db.Column(db.Boolean, default=True)

    # Relationship
    template = db.relationship('Template', backref='items')

# Item History (stores collected values)
class ItemHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    item_id = db.Column(db.Integer, db.ForeignKey('item.id'), nullable=False)
    machine_id = db.Column(db.Integer, db.ForeignKey('machine.id'), nullable=False)
    value = db.Column(db.String(500), nullable=False)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    item = db.relationship('Item', backref='history')
    machine = db.relationship('Machine', backref='item_history')

previous_status = {}

# Enhanced AlertRule (Triggers in Zabbix)
class AlertRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    metric = db.Column(db.String(100), nullable=False)
    operator = db.Column(db.String(10), nullable=False)
    value = db.Column(db.String(100), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    active = db.Column(db.Boolean, default=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)

    # Relationships
    template = db.relationship('Template', backref='alert_rules')
    host_group = db.relationship('HostGroup', backref='alert_rules')

# User management with Flask-Login
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False, unique=True)
    email = db.Column(db.String(200), nullable=False, unique=True)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), default='user')  # admin, user, viewer
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime, nullable=True)

    def check_password(self, password):
        """Vérifie le mot de passe"""
        return check_password_hash(self.password_hash, password)

    def set_password(self, password):
        """Définit un nouveau mot de passe"""
        self.password_hash = generate_password_hash(password)

    def has_permission(self, permission):
        """Vérifie les permissions selon le rôle"""
        permissions = {
            'admin': ['read', 'write', 'delete', 'config', 'user_management'],
            'user': ['read', 'write'],
            'viewer': ['read']
        }
        return permission in permissions.get(self.role, [])

    def is_admin(self):
        return self.role == 'admin'

    def is_active(self):
        return self.active

    def __repr__(self):
        return f'<User {self.username}>'

# Configuration settings
class Config(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), nullable=False, unique=True)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.String(500), nullable=True)
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

# User session history
class UserSession(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    ip_address = db.Column(db.String(50), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    login_time = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    logout_time = db.Column(db.DateTime, nullable=True)

    # Relationship
    user = db.relationship('User', backref='sessions')

# Discovery Rules (Règles de découverte automatique)
class DiscoveryRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    network_range = db.Column(db.String(100), nullable=False)  # ex: ***********/24
    discovery_type = db.Column(db.String(50), default='network')  # network, snmp, agent
    check_interval = db.Column(db.Integer, default=3600)  # en secondes (1h par défaut)
    enabled = db.Column(db.Boolean, default=True)
    last_discovery = db.Column(db.DateTime, nullable=True)
    auto_add_hosts = db.Column(db.Boolean, default=True)  # Ajouter automatiquement les nouveaux hôtes
    default_host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)
    default_template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    snmp_community = db.Column(db.String(100), default='public')
    snmp_version = db.Column(db.String(10), default='2c')
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    default_host_group = db.relationship('HostGroup', backref='discovery_rules')
    default_template = db.relationship('Template', backref='discovery_rules')

# Discovery Events (Événements de découverte)
class DiscoveryEvent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    discovery_rule_id = db.Column(db.Integer, db.ForeignKey('discovery_rule.id'), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)  # host_discovered, host_lost, service_discovered
    ip_address = db.Column(db.String(100), nullable=False)
    hostname = db.Column(db.String(200), nullable=True)
    details = db.Column(db.Text, nullable=True)  # JSON avec détails de découverte
    action_taken = db.Column(db.String(100), nullable=True)  # host_added, alert_created, etc.
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationship
    discovery_rule = db.relationship('DiscoveryRule', backref='events')

# Auto-discovered Hosts (Hôtes découverts automatiquement)
class DiscoveredHost(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    discovery_rule_id = db.Column(db.Integer, db.ForeignKey('discovery_rule.id'), nullable=False)
    ip_address = db.Column(db.String(100), nullable=False)
    hostname = db.Column(db.String(200), nullable=True)
    mac_address = db.Column(db.String(50), nullable=True)
    device_type = db.Column(db.String(100), nullable=True)  # router, switch, server, workstation
    os_info = db.Column(db.String(200), nullable=True)
    snmp_sysname = db.Column(db.String(200), nullable=True)
    snmp_sysdescr = db.Column(db.String(500), nullable=True)
    services_detected = db.Column(db.Text, nullable=True)  # JSON liste des services
    first_discovered = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    last_seen = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    status = db.Column(db.String(20), default='discovered')  # discovered, added, ignored
    machine_id = db.Column(db.Integer, db.ForeignKey('machine.id'), nullable=True)  # Lien vers Machine si ajouté

    # Relationships
    discovery_rule = db.relationship('DiscoveryRule', backref='discovered_hosts')
    machine = db.relationship('Machine', backref='discovered_from')

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def ping(ip):
    param = "-n" if platform.system().lower() == "windows" else "-c"
    command = ["ping", param, "1", ip]
    result = subprocess.run(command, stdout=subprocess.DEVNULL)
    return result.returncode == 0

def snmp_get(ip, oid='*******.*******.0'):
    command = ['snmpget', '-v', '2c', '-c', 'public', '-t', '1', '-r', '1', ip, oid]
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=2)
        if result.returncode != 0 or "No Response" in result.stdout or "Timeout" in result.stdout:
            return None
        
        parts = result.stdout.split(':', 3)
        if len(parts) > 1:
            value = parts[-1].strip()
            if value.startswith('"') and value.endswith('"'):
                return value[1:-1]
            return value
        return None
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return None
    except Exception as e:
        print(f"[ERROR] Command-line snmpget failed for IP {ip} with OID {oid}: {e}")
        return None

# Décorateurs pour les permissions
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Vous devez être administrateur pour accéder à cette page.', 'danger')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated or not current_user.has_permission(permission):
                flash(f'Vous n\'avez pas la permission "{permission}" pour accéder à cette page.', 'danger')
                return redirect(url_for('login', next=request.url))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_cpu_load(ip, description):
    if not description:
        return None

    desc_lower = description.lower()

    oid = None

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        oid = '*******.4.1.2021.11.11.0'  # idle CPU percentage
        idle_str = snmp_get(ip, oid)
        try:
            return 100 - int(idle_str)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        oid = '*******.********.3.1.2.196608'
    elif 'cisco' in desc_lower:
        oid = '*******.*******.*********.1.5'

    if oid:
        val = snmp_get(ip, oid)
        try:
            return int(val)
        except (ValueError, TypeError):
            return None

    return None

def get_memory_usage(ip, description):
    """Get memory usage percentage"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get total and available memory
        total_mem = snmp_get(ip, '*******.4.1.2021.4.5.0')  # memTotalReal
        avail_mem = snmp_get(ip, '*******.4.1.2021.4.6.0')  # memAvailReal
        try:
            total = int(total_mem)
            avail = int(avail_mem)
            used = total - avail
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        # Windows memory usage
        total_mem = snmp_get(ip, '*******.********.2.0')  # hrMemorySize
        try:
            # This is a simplified approach for Windows
            return None  # Would need more complex SNMP walking
        except (ValueError, TypeError):
            return None

    return None

def get_disk_usage(ip, description):
    """Get disk usage percentage for root partition"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get disk usage for root partition
        disk_total = snmp_get(ip, '*******.4.1.2021.*******')  # dskTotal
        disk_used = snmp_get(ip, '*******.4.1.2021.*******')   # dskUsed
        try:
            total = int(disk_total)
            used = int(disk_used)
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None

    return None

def get_uptime(ip):
    """Get system uptime in seconds"""
    uptime_str = snmp_get(ip, '*******.*******.0')  # sysUpTime
    try:
        # sysUpTime is in hundredths of a second
        return int(uptime_str) // 100
    except (ValueError, TypeError):
        return None

def scan_ip(ip_str):
    is_up = ping(ip_str)
    name = None
    cpu_load = None
    memory_usage = None
    disk_usage = None
    uptime = None
    description = None

    if is_up:
        description = snmp_get(ip_str, oid='*******.*******.0')
        name = snmp_get(ip_str)  # sysName

        if description:
            desc_lower = description.lower()

            NON_SERVER_KEYWORDS = ['printer', 'imprimante', 'epson', 'hp', 'canon', 'lexmark', 'xerox', 'scanner', 'plotter']
            SERVER_KEYWORDS = ['server', 'ubuntu', 'debian', 'centos', 'red hat', 'windows', 'linux', 'srv']

            if any(non_kw in desc_lower for non_kw in NON_SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (imprimante ou périphérique - description : {description})")
                return None

            if not any(srv_kw in desc_lower for srv_kw in SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (pas identifié comme un serveur - description : {description})")
                return None

            # Collect all metrics
            cpu_load = get_cpu_load(ip_str, description)
            memory_usage = get_memory_usage(ip_str, description)
            disk_usage = get_disk_usage(ip_str, description)
            uptime = get_uptime(ip_str)

        if not description and not name:
            print(f"[INFO] Pas de réponse SNMP de {ip_str}. Appareil hors ligne ou SNMP non configuré.")

    return {
        "ip": ip_str,
        "status": "up" if is_up else "down",
        "name": name if name else "N/A",
        "cpu_load": cpu_load,
        "memory_usage": memory_usage,
        "disk_usage": disk_usage,
        "uptime": uptime,
        "description": description if description else "N/A"
    }

def scan_network(network_cidr):
    network = ipaddress.ip_network(network_cidr, strict=False)
    ips = [str(ip) for ip in network.hosts()]
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        results = executor.map(scan_ip, ips)
    return list(results)

def get_config_value(key, default=""):
    """Get configuration value from database"""
    config = Config.query.filter_by(key=key).first()
    return config.value if config else default

def safe_db_commit():
    """Commit to database with retry logic for locked database"""
    import time
    max_retries = 3
    for attempt in range(max_retries):
        try:
            db.session.commit()
            return True
        except Exception as e:
            if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                print(f"[WARNING] Base de données verrouillée, tentative {attempt + 1}/{max_retries}")
                db.session.rollback()
                time.sleep(1)  # Attendre 1 seconde avant de réessayer
                continue
            else:
                print(f"[ERROR] Erreur de base de données: {e}")
                db.session.rollback()
                return False
    return False

# ===== SYSTÈME DE DÉCOUVERTE AUTOMATIQUE =====

def discover_host_details(ip):
    """Découvre les détails d'un hôte via SNMP et autres méthodes"""
    details = {
        'ip': ip,
        'hostname': None,
        'mac_address': None,
        'device_type': 'unknown',
        'os_info': None,
        'snmp_sysname': None,
        'snmp_sysdescr': None,
        'services': []
    }

    # Test de connectivité
    if not ping(ip):
        return None

    # Récupération des informations SNMP
    try:
        # sysName (hostname)
        sysname = snmp_get(ip, '*******.*******.0')
        if sysname:
            details['snmp_sysname'] = sysname
            details['hostname'] = sysname

        # sysDescr (description système)
        sysdescr = snmp_get(ip, '*******.*******.0')
        if sysdescr:
            details['snmp_sysdescr'] = sysdescr
            details['os_info'] = sysdescr[:200]  # Limiter la taille

            # Détection du type d'équipement basé sur sysDescr
            sysdescr_lower = sysdescr.lower()
            if 'router' in sysdescr_lower or 'cisco' in sysdescr_lower:
                details['device_type'] = 'router'
            elif 'switch' in sysdescr_lower:
                details['device_type'] = 'switch'
            elif 'linux' in sysdescr_lower or 'ubuntu' in sysdescr_lower or 'centos' in sysdescr_lower:
                details['device_type'] = 'server'
            elif 'windows' in sysdescr_lower:
                details['device_type'] = 'workstation'

        # Détection des services (ports ouverts)
        common_ports = [22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3389, 5432, 3306]
        for port in common_ports:
            if check_port_open(ip, port):
                service_name = get_service_name(port)
                details['services'].append({'port': port, 'service': service_name})

    except Exception as e:
        print(f"[ERROR] Erreur lors de la découverte de {ip}: {e}")

    return details

def check_port_open(ip, port, timeout=2):
    """Vérifie si un port est ouvert"""
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def get_service_name(port):
    """Retourne le nom du service pour un port donné"""
    services = {
        22: 'SSH', 23: 'Telnet', 25: 'SMTP', 53: 'DNS',
        80: 'HTTP', 110: 'POP3', 143: 'IMAP', 443: 'HTTPS',
        993: 'IMAPS', 995: 'POP3S', 3389: 'RDP', 5432: 'PostgreSQL',
        3306: 'MySQL'
    }
    return services.get(port, f'Port-{port}')

def run_discovery_rule(rule_id):
    """Exécute une règle de découverte"""
    rule = DiscoveryRule.query.get(rule_id)
    if not rule or not rule.enabled:
        return {"success": False, "message": "Règle non trouvée ou désactivée"}

    print(f"[INFO] Exécution de la règle de découverte: {rule.name}")

    try:
        # Scanner le réseau défini dans la règle
        network = ipaddress.ip_network(rule.network_range, strict=False)
        discovered_count = 0
        new_hosts_count = 0

        # Parcourir toutes les IPs du réseau
        for ip in network.hosts():
            ip_str = str(ip)

            # Découvrir les détails de l'hôte
            host_details = discover_host_details(ip_str)
            if not host_details:
                continue  # Hôte non accessible

            discovered_count += 1

            # Vérifier si l'hôte est déjà connu
            existing_discovered = DiscoveredHost.query.filter_by(
                discovery_rule_id=rule.id,
                ip_address=ip_str
            ).first()

            if existing_discovered:
                # Mettre à jour l'hôte existant
                existing_discovered.last_seen = datetime.now(timezone.utc)
                existing_discovered.hostname = host_details.get('hostname')
                existing_discovered.snmp_sysname = host_details.get('snmp_sysname')
                existing_discovered.snmp_sysdescr = host_details.get('snmp_sysdescr')
                existing_discovered.device_type = host_details.get('device_type')
                existing_discovered.os_info = host_details.get('os_info')
                existing_discovered.services_detected = str(host_details.get('services', []))
            else:
                # Nouvel hôte découvert
                new_discovered = DiscoveredHost(
                    discovery_rule_id=rule.id,
                    ip_address=ip_str,
                    hostname=host_details.get('hostname'),
                    device_type=host_details.get('device_type'),
                    os_info=host_details.get('os_info'),
                    snmp_sysname=host_details.get('snmp_sysname'),
                    snmp_sysdescr=host_details.get('snmp_sysdescr'),
                    services_detected=str(host_details.get('services', []))
                )
                db.session.add(new_discovered)
                new_hosts_count += 1

                # Créer un événement de découverte
                discovery_event = DiscoveryEvent(
                    discovery_rule_id=rule.id,
                    event_type='host_discovered',
                    ip_address=ip_str,
                    hostname=host_details.get('hostname'),
                    details=str(host_details),
                    action_taken='host_recorded'
                )
                db.session.add(discovery_event)

                # Auto-ajouter l'hôte à la surveillance si configuré
                if rule.auto_add_hosts:
                    add_discovered_host_to_monitoring(new_discovered, rule)

        # Mettre à jour la règle
        rule.last_discovery = datetime.now(timezone.utc)
        db.session.commit()

        print(f"[INFO] Découverte terminée: {discovered_count} hôtes trouvés, {new_hosts_count} nouveaux")

        return {
            "success": True,
            "message": f"Découverte terminée: {discovered_count} hôtes trouvés, {new_hosts_count} nouveaux",
            "discovered_count": discovered_count,
            "new_hosts_count": new_hosts_count
        }

    except Exception as e:
        print(f"[ERROR] Erreur lors de la découverte: {e}")
        return {"success": False, "message": f"Erreur: {str(e)}"}

def add_discovered_host_to_monitoring(discovered_host, rule):
    """Ajoute automatiquement un hôte découvert à la surveillance"""
    try:
        # Vérifier si l'hôte n'est pas déjà en surveillance
        existing_machine = Machine.query.filter_by(ip=discovered_host.ip_address).first()
        if existing_machine:
            discovered_host.machine_id = existing_machine.id
            discovered_host.status = 'added'
            return

        # Créer une nouvelle machine
        new_machine = Machine(
            ip=discovered_host.ip_address,
            name=discovered_host.hostname or discovered_host.ip_address,
            status='up',  # Puisqu'on vient de le découvrir
            description=discovered_host.snmp_sysdescr or f"Auto-découvert par {rule.name}",
            host_group_id=rule.default_host_group_id,
            template_id=rule.default_template_id,
            snmp_community=rule.snmp_community,
            snmp_version=rule.snmp_version,
            monitoring_enabled=True
        )

        db.session.add(new_machine)
        db.session.flush()  # Pour obtenir l'ID

        # Lier l'hôte découvert à la machine
        discovered_host.machine_id = new_machine.id
        discovered_host.status = 'added'

        # Créer un événement
        discovery_event = DiscoveryEvent(
            discovery_rule_id=rule.id,
            event_type='host_added',
            ip_address=discovered_host.ip_address,
            hostname=discovered_host.hostname,
            details=f"Hôte ajouté automatiquement à la surveillance",
            action_taken='machine_created'
        )
        db.session.add(discovery_event)

        # Créer une alerte informative
        info_alert = Alert(
            ip=discovered_host.ip_address,
            name=discovered_host.hostname or discovered_host.ip_address,
            status='up',
            message=f"Nouvel hôte découvert et ajouté automatiquement: {discovered_host.device_type}",
            severity='info'
        )
        db.session.add(info_alert)

        print(f"[INFO] Hôte {discovered_host.ip_address} ajouté automatiquement à la surveillance")

    except Exception as e:
        print(f"[ERROR] Erreur lors de l'ajout automatique de {discovered_host.ip_address}: {e}")

def run_all_discovery_rules():
    """Exécute toutes les règles de découverte actives"""
    rules = DiscoveryRule.query.filter_by(enabled=True).all()
    results = []

    for rule in rules:
        # Vérifier si il est temps d'exécuter cette règle
        if rule.last_discovery:
            time_since_last = datetime.now(timezone.utc) - rule.last_discovery
            if time_since_last.total_seconds() < rule.check_interval:
                continue  # Pas encore temps

        result = run_discovery_rule(rule.id)
        results.append({
            'rule_name': rule.name,
            'result': result
        })

    return results

def setup_email_config(smtp_server, smtp_port, smtp_user, smtp_password, email_from):
    """Quick setup for email configuration"""
    configs = [
        ('smtp_server', smtp_server),
        ('smtp_port', str(smtp_port)),
        ('smtp_user', smtp_user),
        ('smtp_password', smtp_password),
        ('email_from', email_from)
    ]

    for key, value in configs:
        config = Config.query.filter_by(key=key).first()
        if config:
            config.value = value
            config.updated_at = datetime.now(timezone.utc)
        else:
            new_config = Config(key=key, value=value, description=f"Configuration {key}")
            db.session.add(new_config)

    db.session.commit()
    print(f"Configuration email mise à jour: {smtp_user}")
    return True

def send_email(subject, message):
    """Send email using configuration from database"""
    try:
        # Get email configuration from database
        smtp_server = get_config_value('smtp_server')
        smtp_port = get_config_value('smtp_port', '587')
        smtp_user = get_config_value('smtp_user')
        smtp_password = get_config_value('smtp_password')
        email_from = get_config_value('email_from')

        # Check if configuration is complete
        if not smtp_server or not smtp_user or not smtp_password or not email_from:
            print("Configuration email incomplète - email non envoyé")
            return False

        # Check if values are not empty strings
        if not smtp_server.strip() or not smtp_user.strip() or not smtp_password.strip() or not email_from.strip():
            print("Configuration email incomplète - email non envoyé")
            return False

        msg = MIMEMultipart()
        msg['From'] = smtp_user  # Utiliser smtp_user comme expéditeur
        msg['To'] = smtp_user  # Send to admin user
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'plain'))

        server = smtplib.SMTP(smtp_server, int(smtp_port))
        server.starttls()
        server.login(smtp_user, smtp_password)
        server.sendmail(smtp_user, smtp_user, msg.as_string())  # Utiliser smtp_user comme expéditeur
        server.quit()
        print(f"Email envoyé : {subject}")
        return True
    except Exception as e:
        print(f"Erreur envoi email : {e}")
        return False

# Routes d'authentification
@app.route("/login", methods=["GET", "POST"])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")
        remember = bool(request.form.get("remember"))

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active():
            # Enregistrer la session
            session_record = UserSession(
                user_id=user.id,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')[:255]
            )
            db.session.add(session_record)

            # Mettre à jour la dernière connexion
            user.last_login = datetime.now(timezone.utc)
            db.session.commit()

            login_user(user, remember=remember)
            flash(f'Bienvenue {user.username}!', 'success')

            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'danger')

    return render_template("login.html")

@app.route("/logout")
@login_required
def logout():
    # Enregistrer la déconnexion
    if current_user.is_authenticated:
        last_session = UserSession.query.filter_by(
            user_id=current_user.id,
            logout_time=None
        ).order_by(UserSession.login_time.desc()).first()

        if last_session:
            last_session.logout_time = datetime.now(timezone.utc)
            db.session.commit()

    logout_user()
    flash('Vous avez été déconnecté.', 'info')
    return redirect(url_for('login'))

@app.route("/profile")
@login_required
def profile():
    sessions = UserSession.query.filter_by(user_id=current_user.id)\
                               .order_by(UserSession.login_time.desc())\
                               .limit(10).all()
    return render_template("profile.html", user=current_user, sessions=sessions)

@app.route("/change-password", methods=["GET", "POST"])
@login_required
def change_password():
    if request.method == "POST":
        current_password = request.form.get("current_password")
        new_password = request.form.get("new_password")
        confirm_password = request.form.get("confirm_password")

        if not current_user.check_password(current_password):
            flash('Mot de passe actuel incorrect.', 'danger')
        elif new_password != confirm_password:
            flash('Les nouveaux mots de passe ne correspondent pas.', 'danger')
        elif len(new_password) < 6:
            flash('Le mot de passe doit contenir au moins 6 caractères.', 'danger')
        else:
            current_user.set_password(new_password)
            db.session.commit()
            flash('Mot de passe modifié avec succès.', 'success')
            return redirect(url_for('profile'))

    return render_template("change_password.html")

# Gestion des utilisateurs (admin seulement)
@app.route("/users")
@login_required
@admin_required
def manage_users():
    users = User.query.order_by(User.created_at.desc()).all()
    return render_template("users.html", users=users)

@app.route("/users/create", methods=["GET", "POST"])
@login_required
@admin_required
def create_user():
    if request.method == "POST":
        username = request.form.get("username")
        email = request.form.get("email")
        password = request.form.get("password")
        role = request.form.get("role")

        # Vérifications
        if User.query.filter_by(username=username).first():
            flash('Ce nom d\'utilisateur existe déjà.', 'danger')
        elif User.query.filter_by(email=email).first():
            flash('Cette adresse email existe déjà.', 'danger')
        elif len(password) < 6:
            flash('Le mot de passe doit contenir au moins 6 caractères.', 'danger')
        else:
            new_user = User(
                username=username,
                email=email,
                role=role
            )
            new_user.set_password(password)
            db.session.add(new_user)
            db.session.commit()
            flash(f'Utilisateur {username} créé avec succès.', 'success')
            return redirect(url_for('manage_users'))

    return render_template("create_user.html")

@app.route("/users/edit/<int:user_id>", methods=["GET", "POST"])
@login_required
@admin_required
def edit_user(user_id):
    user = User.query.get_or_404(user_id)

    if request.method == "POST":
        user.username = request.form.get("username")
        user.email = request.form.get("email")
        user.role = request.form.get("role")
        user.active = bool(request.form.get("active"))

        new_password = request.form.get("new_password")
        if new_password:
            if len(new_password) < 6:
                flash('Le mot de passe doit contenir au moins 6 caractères.', 'danger')
                return render_template("edit_user.html", user=user)
            user.set_password(new_password)

        db.session.commit()
        flash(f'Utilisateur {user.username} modifié avec succès.', 'success')
        return redirect(url_for('manage_users'))

    return render_template("edit_user.html", user=user)

@app.route("/users/delete/<int:user_id>", methods=["POST"])
@login_required
@admin_required
def delete_user(user_id):
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte.', 'danger')
    else:
        db.session.delete(user)
        db.session.commit()
        flash(f'Utilisateur {user.username} supprimé.', 'success')

    return redirect(url_for('manage_users'))

@app.route("/", methods=["GET"])
@login_required
@permission_required('read')
def index():
    # Redirection vers le dashboard comme dans Zabbix
    return redirect(url_for('dashboard'))

@app.route("/scan", methods=["GET", "POST"])
@login_required
@permission_required('read')
def scan_page():
    alerts = []
    error = None
    global previous_status

    if request.method == "POST":
        network_cidr = request.form.get("network_cidr")
        try:
            # Validate network CIDR
            ipaddress.ip_network(network_cidr, strict=False)
            print(f"[INFO] Début du scan du réseau {network_cidr}")
            scanned = scan_network(network_cidr)
            scan_timestamp = datetime.now(timezone.utc)
            print(f"[INFO] Scan terminé, {len([m for m in scanned if m is not None])} machines trouvées")

            # Save scan results to database
            try:
                for machine in scanned:
                    if machine is None:
                        continue  # Ignore non-servers

                    entry = Machine(
                        ip=machine["ip"],
                        name=machine["name"],
                        status=machine["status"],
                        cpu_load=machine["cpu_load"],
                        memory_usage=machine["memory_usage"],
                        disk_usage=machine["disk_usage"],
                        uptime=machine["uptime"],
                        timestamp=scan_timestamp,
                        description=machine["description"]
                    )
                    db.session.add(entry)
                if safe_db_commit():
                    print(f"[INFO] Données sauvegardées en base de données")
                else:
                    print(f"[ERROR] Échec de la sauvegarde en base de données")
            except Exception as e:
                print(f"[ERROR] Erreur lors de la sauvegarde en base : {e}")
                db.session.rollback()

            session['last_scan_time'] = scan_timestamp.isoformat()
            session['last_ip_searched'] = network_cidr

            # Gestion alertes changement d'état simple
            try:
                current_status = {m["ip"]: m["status"] for m in scanned if m is not None}
                for machine in scanned:
                    if machine is None:
                        continue
                    ip = machine["ip"]
                    curr = machine["status"]
                    prev = previous_status.get(ip)

                    if prev and prev != curr:
                        if curr == "down":
                            alert_msg = f"⚠️ {ip} ({machine['name']}) est maintenant HORS LIGNE."
                            severity = "high"
                        else:
                            alert_msg = f"✅ {ip} ({machine['name']}) est de nouveau EN LIGNE."
                            severity = "info"

                        try:
                            last_alert = Alert.query.filter_by(ip=ip, status=curr, acknowledged=False).order_by(Alert.timestamp.desc()).first()
                            if not last_alert or (datetime.now(timezone.utc) - last_alert.timestamp).total_seconds() > 300:
                                new_alert = Alert(ip=ip, name=machine['name'], status=curr, message=alert_msg, severity=severity)
                                db.session.add(new_alert)
                                safe_db_commit()
                                send_email(
                                    f"[{severity.upper()}] Alerte Monitoring - Machine " + ("hors ligne" if curr == "down" else "en ligne"),
                                    alert_msg
                                )
                                alerts.append(alert_msg)
                        except Exception as e:
                            print(f"[ERROR] Erreur lors de la création d'alerte pour {ip}: {e}")
                            db.session.rollback()

                previous_status = current_status
            except Exception as e:
                print(f"[ERROR] Erreur lors du traitement des alertes: {e}")

            # Appliquer règles personnalisées
            try:
                from jinja2 import Template
                rules = AlertRule.query.filter_by(active=True).all()
                print(f"[INFO] Application de {len(rules)} règles d'alerte")

                for machine in scanned:
                    if machine is None:
                        continue
                    context = {
                        "ip": machine["ip"],
                        "status": machine["status"],
                        "name": machine["name"],
                        "cpu_load": machine["cpu_load"],
                        "memory_usage": machine["memory_usage"],
                        "disk_usage": machine["disk_usage"],
                        "uptime": machine["uptime"],
                        "description": machine["description"]
                    }
                    for rule in rules:
                        try:
                            metric_value = context.get(rule.metric)
                            if metric_value is None:
                                continue

                            match = False
                            is_numeric_comparison = rule.operator in ('>', '<', '>=', '<=')

                            if is_numeric_comparison:
                                try:
                                    metric_val_num = float(metric_value)
                                    rule_val_num = float(rule.value)
                                    if rule.operator == '>' and metric_val_num > rule_val_num: match = True
                                    elif rule.operator == '<' and metric_val_num < rule_val_num: match = True
                                    elif rule.operator == '>=' and metric_val_num >= rule_val_num: match = True
                                    elif rule.operator == '<=' and metric_val_num <= rule_val_num: match = True
                                except (ValueError, TypeError):
                                    pass
                            else:
                                if rule.operator == '==' and str(metric_value) == rule.value:
                                    match = True
                                elif rule.operator == '!=' and str(metric_value) != rule.value:
                                    match = True

                            if match:
                                template = Template(rule.message)
                                alert_msg = template.render(**context)

                                last_alert = Alert.query.filter_by(ip=machine["ip"], message=alert_msg, acknowledged=False)\
                                                         .order_by(Alert.timestamp.desc()).first()

                                if not last_alert or (datetime.now(timezone.utc) - last_alert.timestamp).total_seconds() > 300:
                                    new_alert = Alert(ip=machine["ip"], name=machine["name"], status=machine["status"],
                                                    message=alert_msg, severity=rule.severity)
                                    db.session.add(new_alert)
                                    safe_db_commit()

                                    alerts.append(alert_msg)
                                    send_email(f"[{rule.severity.upper()}] Alerte Monitoring: {rule.name}", alert_msg)
                        except Exception as e:
                            print(f"[ERROR] Erreur lors de l'évaluation de la règle {rule.name}: {e}")
                            continue
            except Exception as e:
                print(f"[ERROR] Erreur lors de l'application des règles: {e}")

            # Filtrer les résultats None avant de les passer au template
            filtered_results = [machine for machine in scanned if machine is not None]
            return render_template("scan.html", results=filtered_results, alerts=alerts, error=error, ip_input=network_cidr)

        except ValueError:
            error = "Format invalide. Exemple attendu : ***********/24"
            print(f"[ERROR] Format réseau invalide: {network_cidr}")
        except Exception as e:
            error = f"Erreur lors du scan: {str(e)}"
            print(f"[ERROR] Erreur générale lors du scan: {e}")
            import traceback
            traceback.print_exc()

    results = []
    ip_input = ""
    if 'last_scan_time' in session:
        scan_time = datetime.fromisoformat(session['last_scan_time'])
        time_start = scan_time - timedelta(seconds=10)
        time_end = scan_time + timedelta(seconds=10)
        results = Machine.query.filter(Machine.timestamp.between(time_start, time_end)).all()

    if 'last_ip_searched' in session:
        ip_input = session['last_ip_searched']

    return render_template("scan.html", results=results, alerts=alerts, error=error, ip_input=ip_input)

@app.route("/historique")
@login_required
@permission_required('read')
def historique():
    machines = Machine.query.order_by(Machine.timestamp.desc()).limit(250).all()
    return render_template("historique.html", machines=machines)
@app.route("/machine/<ip>")
@login_required
@permission_required('read')
def machine_detail(ip):
    history = Machine.query.filter(Machine.ip == ip).order_by(Machine.timestamp.asc()).all()

    last_entry = Machine.query.filter_by(ip=ip).order_by(Machine.timestamp.desc()).first()
    machine_name = last_entry.name if last_entry else "N/A"
    machine_description = last_entry.description if last_entry else "N/A"

    chart_labels = [h.timestamp.strftime('%Y-%m-%d %H:%M:%S') for h in history]
    chart_cpu_data = [h.cpu_load if h.cpu_load is not None else 0 for h in history]
    chart_memory_data = [h.memory_usage if h.memory_usage is not None else 0 for h in history]
    chart_disk_data = [h.disk_usage if h.disk_usage is not None else 0 for h in history]

    return render_template("machine_detail.html",
                           machine_ip=ip,
                           machine_name=machine_name,
                           machine_description=machine_description,
                           history=history,
                           chart_labels=chart_labels,
                           chart_cpu_data=chart_cpu_data,
                           chart_memory_data=chart_memory_data,
                           chart_disk_data=chart_disk_data)


@app.route("/alerts")
@login_required
@permission_required('read')
def alerts():
    alerts = Alert.query.order_by(Alert.timestamp.desc()).limit(100).all()
    return render_template("alerts.html", alerts=alerts)

@app.route("/alert/acknowledge/<int:alert_id>", methods=["POST"])
@login_required
@permission_required('write')
def acknowledge_alert(alert_id):
    alert = Alert.query.get_or_404(alert_id)
    alert.acknowledged = True
    alert.ack_timestamp = datetime.now(timezone.utc)
    alert.ack_user = "admin"
    db.session.commit()
    return redirect(url_for('alerts'))

from sqlalchemy import func

@app.route("/dashboard")
@login_required
@permission_required('read')
def dashboard():
    now = datetime.now(timezone.utc)
    start_time = now - timedelta(hours=24)

    results = db.session.query(
        func.strftime('%Y-%m-%d %H:00:00', Machine.timestamp).label('hour'),
        func.avg(Machine.cpu_load).label('avg_cpu')
    ).filter(
        Machine.timestamp >= start_time,
        Machine.cpu_load != None
    ).group_by('hour').order_by('hour').all()

    cpu_labels = [r.hour for r in results]
    cpu_data = [round(r.avg_cpu, 2) for r in results]

    total_machines = Machine.query.count()
    up_count = Machine.query.filter_by(status="up").count()
    down_count = total_machines - up_count
    alert_count = Alert.query.filter(Alert.timestamp >= now.replace(hour=0, minute=0, second=0)).count()

    # Get top CPU usage hosts
    top_cpu = Machine.query.filter(Machine.cpu_load.isnot(None)).order_by(Machine.cpu_load.desc()).limit(10).all()

    # Get unacknowledged alerts
    unack_alerts = Alert.query.filter_by(acknowledged=False).order_by(Alert.timestamp.desc()).limit(10).all()

    return render_template("dashboard.html",
                           total_machines=total_machines,
                           up_count=up_count,
                           down_count=down_count,
                           alert_count=alert_count,
                           cpu_labels=json.dumps(cpu_labels),
                           cpu_data=json.dumps(cpu_data),
                           top_cpu=top_cpu,
                           alerts=unack_alerts)

@app.route("/api/dashboard/stats")
def dashboard_stats_api():
    """API endpoint for real-time dashboard statistics"""
    now = datetime.now(timezone.utc)

    # Get basic stats
    total_machines = Machine.query.count()
    up_count = Machine.query.filter_by(status="up").count()
    down_count = total_machines - up_count
    alert_count = Alert.query.filter(Alert.timestamp >= now.replace(hour=0, minute=0, second=0)).count()
    unack_alert_count = Alert.query.filter_by(acknowledged=False).count()

    # Get recent alerts
    recent_alerts = Alert.query.filter_by(acknowledged=False).order_by(Alert.timestamp.desc()).limit(5).all()

    # Calculate system status
    if down_count > total_machines * 0.5:
        system_status = "critical"
        status_text = "🔴 Système critique"
    elif down_count > 0 or unack_alert_count > 10:
        system_status = "warning"
        status_text = "🟡 Attention requise"
    else:
        system_status = "ok"
        status_text = "🟢 Système opérationnel"

    return {
        "total_machines": total_machines,
        "up_count": up_count,
        "down_count": down_count,
        "alert_count": alert_count,
        "unack_alert_count": unack_alert_count,
        "system_status": system_status,
        "status_text": status_text,
        "recent_alerts": [
            {
                "id": alert.id,
                "ip": alert.ip,
                "name": alert.name,
                "message": alert.message,
                "severity": alert.severity,
                "timestamp": alert.timestamp.strftime('%d/%m/%Y %H:%M:%S')
            } for alert in recent_alerts
        ],
        "timestamp": now.strftime('%d/%m/%Y %H:%M:%S')
    }


@app.route("/rules", methods=["GET", "POST"])
@login_required
@permission_required('read')
def manage_rules():
    if request.method == "POST":
        name = request.form.get("name")
        metric = request.form.get("metric")
        operator = request.form.get("operator")
        value = request.form.get("value")
        message = request.form.get("message")
        severity = request.form.get("severity", "warning")
        new_rule = AlertRule(name=name, metric=metric, operator=operator, value=value, message=message, severity=severity)
        db.session.add(new_rule)
        db.session.commit()
        return redirect(url_for("manage_rules"))

    rules = AlertRule.query.all()
    return render_template("rules.html", rules=rules)

# ===== ROUTES POUR LA DÉCOUVERTE =====

@app.route("/discovery")
@login_required
@permission_required('read')
def discovery_dashboard():
    """Dashboard de découverte"""
    rules = DiscoveryRule.query.all()
    recent_events = DiscoveryEvent.query.order_by(DiscoveryEvent.timestamp.desc()).limit(20).all()
    discovered_hosts = DiscoveredHost.query.filter_by(status='discovered').limit(50).all()

    # Statistiques
    total_rules = len(rules)
    active_rules = len([r for r in rules if r.enabled])
    total_discovered = DiscoveredHost.query.count()
    new_today = DiscoveredHost.query.filter(
        DiscoveredHost.first_discovered >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0)
    ).count()

    stats = {
        'total_rules': total_rules,
        'active_rules': active_rules,
        'total_discovered': total_discovered,
        'new_today': new_today
    }

    return render_template("discovery.html",
                         rules=rules,
                         recent_events=recent_events,
                         discovered_hosts=discovered_hosts,
                         stats=stats)

@app.route("/discovery/rules", methods=["GET", "POST"])
@login_required
@permission_required('write')
def discovery_rules():
    """Gestion des règles de découverte"""
    if request.method == "POST":
        name = request.form.get("name")
        network_range = request.form.get("network_range")
        check_interval = int(request.form.get("check_interval", 3600))
        auto_add_hosts = request.form.get("auto_add_hosts") == "on"
        default_host_group_id = request.form.get("default_host_group_id")
        default_template_id = request.form.get("default_template_id")
        snmp_community = request.form.get("snmp_community", "public")

        # Validation du réseau
        try:
            ipaddress.ip_network(network_range, strict=False)
        except ValueError:
            flash("Format de réseau invalide", "danger")
            return redirect(url_for("discovery_rules"))

        new_rule = DiscoveryRule(
            name=name,
            network_range=network_range,
            check_interval=check_interval,
            auto_add_hosts=auto_add_hosts,
            default_host_group_id=int(default_host_group_id) if default_host_group_id else None,
            default_template_id=int(default_template_id) if default_template_id else None,
            snmp_community=snmp_community
        )

        db.session.add(new_rule)
        db.session.commit()
        flash(f"Règle de découverte '{name}' créée avec succès", "success")
        return redirect(url_for("discovery_rules"))

    rules = DiscoveryRule.query.all()
    host_groups = HostGroup.query.all()
    templates = Template.query.all()

    return render_template("discovery_rules.html",
                         rules=rules,
                         host_groups=host_groups,
                         templates=templates)

@app.route("/discovery/rule/<int:rule_id>/run", methods=["POST"])
@login_required
@permission_required('write')
def run_discovery_rule_manual(rule_id):
    """Exécuter manuellement une règle de découverte"""
    result = run_discovery_rule(rule_id)

    if result["success"]:
        flash(result["message"], "success")
    else:
        flash(result["message"], "danger")

    return redirect(url_for("discovery_dashboard"))

@app.route("/discovery/rule/<int:rule_id>/toggle", methods=["POST"])
@login_required
@permission_required('write')
def toggle_discovery_rule(rule_id):
    """Activer/désactiver une règle de découverte"""
    rule = DiscoveryRule.query.get_or_404(rule_id)
    rule.enabled = not rule.enabled
    rule.updated_at = datetime.now(timezone.utc)
    db.session.commit()

    status = "activée" if rule.enabled else "désactivée"
    flash(f"Règle '{rule.name}' {status}", "success")
    return redirect(url_for("discovery_rules"))

@app.route("/discovery/rule/<int:rule_id>/delete", methods=["POST"])
@login_required
@permission_required('write')
def delete_discovery_rule(rule_id):
    """Supprimer une règle de découverte"""
    rule = DiscoveryRule.query.get_or_404(rule_id)
    rule_name = rule.name

    # Supprimer les événements et hôtes associés
    DiscoveryEvent.query.filter_by(discovery_rule_id=rule_id).delete()
    DiscoveredHost.query.filter_by(discovery_rule_id=rule_id).delete()

    db.session.delete(rule)
    db.session.commit()

    flash(f"Règle '{rule_name}' supprimée", "success")
    return redirect(url_for("discovery_rules"))

@app.route("/discovery/hosts")
@login_required
@permission_required('read')
def discovered_hosts():
    """Liste des hôtes découverts"""
    hosts = DiscoveredHost.query.order_by(DiscoveredHost.last_seen.desc()).all()
    return render_template("discovered_hosts.html", hosts=hosts)

@app.route("/discovery/host/<int:host_id>/add", methods=["POST"])
@login_required
@permission_required('write')
def add_discovered_host_manual(host_id):
    """Ajouter manuellement un hôte découvert à la surveillance"""
    discovered_host = DiscoveredHost.query.get_or_404(host_id)

    if discovered_host.status == 'added':
        flash("Cet hôte est déjà en surveillance", "warning")
        return redirect(url_for("discovered_hosts"))

    try:
        add_discovered_host_to_monitoring(discovered_host, discovered_host.discovery_rule)
        db.session.commit()
        flash(f"Hôte {discovered_host.ip_address} ajouté à la surveillance", "success")
    except Exception as e:
        flash(f"Erreur lors de l'ajout: {str(e)}", "danger")

    return redirect(url_for("discovered_hosts"))

@app.route("/discovery/host/<int:host_id>/ignore", methods=["POST"])
@login_required
@permission_required('write')
def ignore_discovered_host(host_id):
    """Ignorer un hôte découvert"""
    discovered_host = DiscoveredHost.query.get_or_404(host_id)
    discovered_host.status = 'ignored'
    db.session.commit()

    flash(f"Hôte {discovered_host.ip_address} ignoré", "success")
    return redirect(url_for("discovered_hosts"))

@app.route("/api/discovery/run-all", methods=["POST"])
@login_required
@permission_required('write')
def api_run_all_discovery():
    """API pour exécuter toutes les règles de découverte"""
    results = run_all_discovery_rules()
    return {"success": True, "results": results}

@app.route("/rules/delete/<int:rule_id>", methods=["POST"])
def delete_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    db.session.delete(rule)
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/toggle/<int:rule_id>", methods=["POST"])
def toggle_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    rule.active = not rule.active
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/edit/<int:rule_id>", methods=["GET", "POST"])
def edit_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    if request.method == "POST":
        rule.name = request.form.get("name")
        rule.metric = request.form.get("metric")
        rule.operator = request.form.get("operator")
        rule.value = request.form.get("value")
        rule.message = request.form.get("message")
        rule.severity = request.form.get("severity", "warning")
        db.session.commit()
        return redirect(url_for("manage_rules"))
    return render_template("edit_rule.html", rule=rule)


# Host Groups Management
@app.route("/hostgroups", methods=["GET", "POST"])
def manage_hostgroups():
    if request.method == "POST":
        name = request.form.get("name")
        description = request.form.get("description", "")
        new_group = HostGroup(name=name, description=description)
        db.session.add(new_group)
        db.session.commit()
        return redirect(url_for("manage_hostgroups"))

    groups = HostGroup.query.all()
    return render_template("hostgroups.html", groups=groups)

@app.route("/hostgroups/delete/<int:group_id>", methods=["POST"])
def delete_hostgroup(group_id):
    group = HostGroup.query.get_or_404(group_id)
    # Update machines to remove group assignment
    Machine.query.filter_by(host_group_id=group_id).update({Machine.host_group_id: None})
    db.session.delete(group)
    db.session.commit()
    return redirect(url_for("manage_hostgroups"))

@app.route("/hostgroups/edit/<int:group_id>", methods=["GET", "POST"])
def edit_hostgroup(group_id):
    group = HostGroup.query.get_or_404(group_id)
    if request.method == "POST":
        group.name = request.form.get("name")
        group.description = request.form.get("description", "")
        db.session.commit()
        return redirect(url_for("manage_hostgroups"))
    return render_template("edit_hostgroup.html", group=group)

# Templates Management
@app.route("/templates", methods=["GET", "POST"])
def manage_templates():
    if request.method == "POST":
        name = request.form.get("name")
        description = request.form.get("description", "")
        new_template = Template(name=name, description=description)
        db.session.add(new_template)
        db.session.commit()
        return redirect(url_for("manage_templates"))

    templates = Template.query.all()
    return render_template("templates.html", templates=templates)

@app.route("/templates/delete/<int:template_id>", methods=["POST"])
def delete_template(template_id):
    template = Template.query.get_or_404(template_id)
    # Update machines to remove template assignment
    Machine.query.filter_by(template_id=template_id).update({Machine.template_id: None})
    db.session.delete(template)
    db.session.commit()
    return redirect(url_for("manage_templates"))

@app.route("/templates/edit/<int:template_id>", methods=["GET", "POST"])
def edit_template(template_id):
    template = Template.query.get_or_404(template_id)
    if request.method == "POST":
        template.name = request.form.get("name")
        template.description = request.form.get("description", "")
        db.session.commit()
        return redirect(url_for("manage_templates"))
    return render_template("edit_template.html", template=template)

# Host Management
@app.route("/hosts")
def manage_hosts():
    hosts = Machine.query.order_by(Machine.timestamp.desc()).limit(100).all()
    groups = HostGroup.query.all()
    templates = Template.query.all()
    return render_template("hosts.html", hosts=hosts, groups=groups, templates=templates)

@app.route("/hosts/assign/<int:machine_id>", methods=["POST"])
def assign_host_group_template(machine_id):
    machine = Machine.query.get_or_404(machine_id)
    group_id = request.form.get("group_id")
    template_id = request.form.get("template_id")

    if group_id and group_id != "":
        machine.host_group_id = int(group_id) if group_id != "none" else None
    if template_id and template_id != "":
        machine.template_id = int(template_id) if template_id != "none" else None

    db.session.commit()
    return redirect(url_for("manage_hosts"))

# Configuration Management
@app.route("/config", methods=["GET", "POST"])
@login_required
@admin_required
def manage_config():
    if request.method == "POST":
        # Update configuration values
        for key, value in request.form.items():
            if key.startswith('config_'):
                config_key = key.replace('config_', '')
                config = Config.query.filter_by(key=config_key).first()
                if config:
                    config.value = value
                    config.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        return redirect(url_for("manage_config"))

    # Get all configuration items
    configs = Config.query.all()
    config_dict = {config.key: config for config in configs}

    return render_template("config.html", configs=config_dict)

@app.route("/config/quick-setup", methods=["POST"])
def quick_email_setup():
    """Quick email setup for testing"""
    try:
        # Example configuration - MODIFY THESE VALUES
        setup_email_config(
            smtp_server="smtp.gmail.com",
            smtp_port=587,
            smtp_user="<EMAIL>",
            smtp_password="mxxq xxwx ejqi yfqj",
            email_from="<EMAIL>"  # Utilisez la même adresse
        )
        return {"success": True, "message": "Configuration email configurée rapidement"}
    except Exception as e:
        return {"success": False, "message": f"Erreur: {str(e)}"}

@app.route("/config/test-email", methods=["POST"])
def test_email():
    """Test email configuration"""
    try:
        # Get current email settings
        smtp_server = Config.query.filter_by(key='smtp_server').first()
        smtp_port = Config.query.filter_by(key='smtp_port').first()
        smtp_user = Config.query.filter_by(key='smtp_user').first()
        smtp_password = Config.query.filter_by(key='smtp_password').first()
        email_from = Config.query.filter_by(key='email_from').first()

        if not all([smtp_server, smtp_port, smtp_user, smtp_password, email_from]):
            return {"success": False, "message": "Configuration email incomplète"}

        # Send test email
        msg = MIMEMultipart()
        msg['From'] = email_from.value
        msg['To'] = smtp_user.value  # Send to self for testing
        msg['Subject'] = "Test de configuration email - Monitoring"

        body = """
        Ceci est un email de test pour vérifier la configuration SMTP.

        Si vous recevez cet email, la configuration est correcte.

        Système de monitoring réseau
        """
        msg.attach(MIMEText(body, 'plain'))

        server = smtplib.SMTP(smtp_server.value, int(smtp_port.value))
        server.starttls()
        server.login(smtp_user.value, smtp_password.value)
        server.sendmail(email_from.value, smtp_user.value, msg.as_string())
        server.quit()

        return {"success": True, "message": "Email de test envoyé avec succès"}

    except Exception as e:
        return {"success": False, "message": f"Erreur: {str(e)}"}

# Data Retention and Cleanup (Housekeeping)
@app.route("/housekeeping", methods=["GET", "POST"])
@login_required
@admin_required
def housekeeping():
    if request.method == "POST":
        action = request.form.get("action")

        if action == "cleanup_old_data":
            result = cleanup_old_data()
            return {"success": True, "message": result}
        elif action == "cleanup_alerts":
            result = cleanup_old_alerts()
            return {"success": True, "message": result}
        elif action == "cleanup_history":
            result = cleanup_old_history()
            return {"success": True, "message": result}

    # Get statistics
    stats = get_database_stats()
    return render_template("housekeeping.html", stats=stats)

def cleanup_old_data():
    """Clean up old data based on retention policy"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    # Clean up old machine records
    old_machines = Machine.query.filter(Machine.timestamp < cutoff_date).count()
    Machine.query.filter(Machine.timestamp < cutoff_date).delete()

    # Clean up old item history
    old_history = ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count()
    ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).delete()

    db.session.commit()

    return f"Supprimé {old_machines} enregistrements de machines et {old_history} enregistrements d'historique (plus de {retention_days} jours)"

def cleanup_old_alerts():
    """Clean up acknowledged alerts older than retention period"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    # Only delete acknowledged alerts
    old_alerts = Alert.query.filter(
        Alert.acknowledged == True,
        Alert.timestamp < cutoff_date
    ).count()

    Alert.query.filter(
        Alert.acknowledged == True,
        Alert.timestamp < cutoff_date
    ).delete()

    db.session.commit()

    return f"Supprimé {old_alerts} alertes acquittées anciennes"

def cleanup_old_history():
    """Clean up old item history keeping only recent data"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    old_history = ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count()
    ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).delete()

    db.session.commit()

    return f"Supprimé {old_history} enregistrements d'historique d'items"

def get_database_stats():
    """Get database statistics for housekeeping page"""
    now = datetime.now(timezone.utc)
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = now - timedelta(days=retention_days)

    stats = {
        'total_machines': Machine.query.count(),
        'old_machines': Machine.query.filter(Machine.timestamp < cutoff_date).count(),
        'total_alerts': Alert.query.count(),
        'old_acknowledged_alerts': Alert.query.filter(
            Alert.acknowledged == True,
            Alert.timestamp < cutoff_date
        ).count(),
        'unacknowledged_alerts': Alert.query.filter(Alert.acknowledged == False).count(),
        'total_history': ItemHistory.query.count(),
        'old_history': ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count(),
        'retention_days': retention_days,
        'cutoff_date': cutoff_date.strftime('%d/%m/%Y %H:%M')
    }

    return stats

# Automatic cleanup scheduler (can be called by cron job)
@app.route("/api/housekeeping/auto", methods=["POST"])
def auto_housekeeping():
    """Automatic housekeeping endpoint for scheduled cleanup"""
    try:
        result_data = cleanup_old_data()
        result_alerts = cleanup_old_alerts()
        result_history = cleanup_old_history()

        return {
            "success": True,
            "message": "Nettoyage automatique effectué",
            "details": {
                "data": result_data,
                "alerts": result_alerts,
                "history": result_history
            }
        }
    except Exception as e:
        return {"success": False, "message": f"Erreur lors du nettoyage: {str(e)}"}

# Export/Import Functionality
@app.route("/export-import")
def export_import():
    return render_template("export_import.html")

@app.route("/export/<export_type>")
def export_data(export_type):
    """Export configuration data"""
    try:
        if export_type == "hostgroups":
            data = export_hostgroups()
        elif export_type == "templates":
            data = export_templates()
        elif export_type == "rules":
            data = export_rules()
        elif export_type == "config":
            data = export_config()
        elif export_type == "all":
            data = export_all()
        else:
            return {"error": "Type d'export invalide"}, 400

        from flask import Response
        import json

        response = Response(
            json.dumps(data, indent=2, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': f'attachment; filename=monitoring_{export_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'}
        )
        return response

    except Exception as e:
        return {"error": f"Erreur lors de l'export: {str(e)}"}, 500

@app.route("/import", methods=["POST"])
def import_data():
    """Import configuration data"""
    try:
        if 'file' not in request.files:
            return {"success": False, "message": "Aucun fichier sélectionné"}

        file = request.files['file']
        if file.filename == '':
            return {"success": False, "message": "Aucun fichier sélectionné"}

        if not file.filename.endswith('.json'):
            return {"success": False, "message": "Le fichier doit être au format JSON"}

        import json
        data = json.load(file)

        result = import_configuration(data)
        return {"success": True, "message": result}

    except Exception as e:
        return {"success": False, "message": f"Erreur lors de l'import: {str(e)}"}

def export_hostgroups():
    """Export host groups"""
    groups = HostGroup.query.all()
    return {
        "type": "hostgroups",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": group.name,
                "description": group.description,
                "created_at": group.created_at.isoformat() if group.created_at else None
            }
            for group in groups
        ]
    }

def export_templates():
    """Export templates with their items and rules"""
    templates = Template.query.all()
    return {
        "type": "templates",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": template.name,
                "description": template.description,
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "items": [
                    {
                        "name": item.name,
                        "key": item.key,
                        "oid": item.oid,
                        "value_type": item.value_type,
                        "units": item.units,
                        "description": item.description,
                        "active": item.active
                    }
                    for item in template.items
                ],
                "alert_rules": [
                    {
                        "name": rule.name,
                        "metric": rule.metric,
                        "operator": rule.operator,
                        "value": rule.value,
                        "message": rule.message,
                        "severity": rule.severity,
                        "active": rule.active
                    }
                    for rule in template.alert_rules
                ]
            }
            for template in templates
        ]
    }

def export_rules():
    """Export alert rules"""
    rules = AlertRule.query.all()
    return {
        "type": "rules",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": rule.name,
                "metric": rule.metric,
                "operator": rule.operator,
                "value": rule.value,
                "message": rule.message,
                "severity": rule.severity,
                "active": rule.active,
                "template_name": rule.template.name if rule.template else None,
                "hostgroup_name": rule.host_group.name if rule.host_group else None
            }
            for rule in rules
        ]
    }

def export_config():
    """Export system configuration"""
    configs = Config.query.all()
    return {
        "type": "config",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "key": config.key,
                "value": config.value,
                "description": config.description
            }
            for config in configs
        ]
    }

def export_all():
    """Export all configuration data"""
    return {
        "type": "all",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "hostgroups": export_hostgroups()["data"],
        "templates": export_templates()["data"],
        "rules": export_rules()["data"],
        "config": export_config()["data"]
    }

def import_configuration(data):
    """Import configuration data from JSON"""
    imported_count = 0

    try:
        # Import host groups
        if "hostgroups" in data:
            for group_data in data["hostgroups"]:
                existing = HostGroup.query.filter_by(name=group_data["name"]).first()
                if not existing:
                    new_group = HostGroup(
                        name=group_data["name"],
                        description=group_data.get("description", "")
                    )
                    db.session.add(new_group)
                    imported_count += 1

        # Import templates
        if "templates" in data:
            for template_data in data["templates"]:
                existing = Template.query.filter_by(name=template_data["name"]).first()
                if not existing:
                    new_template = Template(
                        name=template_data["name"],
                        description=template_data.get("description", "")
                    )
                    db.session.add(new_template)
                    db.session.flush()  # Get the ID

                    # Import items for this template
                    for item_data in template_data.get("items", []):
                        new_item = Item(
                            name=item_data["name"],
                            key=item_data["key"],
                            oid=item_data.get("oid"),
                            value_type=item_data.get("value_type", "numeric"),
                            units=item_data.get("units"),
                            description=item_data.get("description"),
                            template_id=new_template.id,
                            active=item_data.get("active", True)
                        )
                        db.session.add(new_item)

                    # Import alert rules for this template
                    for rule_data in template_data.get("alert_rules", []):
                        new_rule = AlertRule(
                            name=rule_data["name"],
                            metric=rule_data["metric"],
                            operator=rule_data["operator"],
                            value=rule_data["value"],
                            message=rule_data["message"],
                            severity=rule_data.get("severity", "warning"),
                            template_id=new_template.id,
                            active=rule_data.get("active", True)
                        )
                        db.session.add(new_rule)

                    imported_count += 1

        # Import standalone rules
        if "rules" in data:
            for rule_data in data["rules"]:
                existing = AlertRule.query.filter_by(name=rule_data["name"]).first()
                if not existing:
                    # Find template and hostgroup by name if specified
                    template_id = None
                    hostgroup_id = None

                    if rule_data.get("template_name"):
                        template = Template.query.filter_by(name=rule_data["template_name"]).first()
                        if template:
                            template_id = template.id

                    if rule_data.get("hostgroup_name"):
                        hostgroup = HostGroup.query.filter_by(name=rule_data["hostgroup_name"]).first()
                        if hostgroup:
                            hostgroup_id = hostgroup.id

                    new_rule = AlertRule(
                        name=rule_data["name"],
                        metric=rule_data["metric"],
                        operator=rule_data["operator"],
                        value=rule_data["value"],
                        message=rule_data["message"],
                        severity=rule_data.get("severity", "warning"),
                        template_id=template_id,
                        host_group_id=hostgroup_id,
                        active=rule_data.get("active", True)
                    )
                    db.session.add(new_rule)
                    imported_count += 1

        # Import configuration
        if "config" in data:
            for config_data in data["config"]:
                existing = Config.query.filter_by(key=config_data["key"]).first()
                if existing:
                    existing.value = config_data["value"]
                    existing.updated_at = datetime.now(timezone.utc)
                else:
                    new_config = Config(
                        key=config_data["key"],
                        value=config_data["value"],
                        description=config_data.get("description", "")
                    )
                    db.session.add(new_config)
                imported_count += 1

        db.session.commit()
        return f"Import réussi: {imported_count} éléments importés"

    except Exception as e:
        db.session.rollback()
        raise e

def init_database():
    """Initialise la base de données avec les données par défaut"""
    with app.app_context():
        db.create_all()

        # Créer un utilisateur admin par défaut s'il n'existe pas
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("[INFO] Utilisateur admin créé (admin/admin123)")

        # Créer des groupes d'hôtes par défaut
        default_groups = [
            {'name': 'Serveurs', 'description': 'Serveurs de production'},
            {'name': 'Réseau', 'description': 'Équipements réseau (routeurs, switches)'},
            {'name': 'Postes de travail', 'description': 'Ordinateurs des utilisateurs'},
            {'name': 'Découverte Auto', 'description': 'Hôtes découverts automatiquement'}
        ]

        for group_data in default_groups:
            existing_group = HostGroup.query.filter_by(name=group_data['name']).first()
            if not existing_group:
                new_group = HostGroup(name=group_data['name'], description=group_data['description'])
                db.session.add(new_group)

        # Créer des templates par défaut
        default_templates = [
            {
                'name': 'Linux Server',
                'description': 'Template pour serveurs Linux',
                'items': [
                    {'name': 'CPU Load', 'key': 'system.cpu.load', 'oid': '*******.4.1.2021.********'},
                    {'name': 'Memory Usage', 'key': 'vm.memory.size', 'oid': '*******.4.1.2021.4.6.0'},
                    {'name': 'Disk Usage', 'key': 'vfs.fs.size', 'oid': '*******.4.1.2021.*******'}
                ]
            },
            {
                'name': 'Network Device',
                'description': 'Template pour équipements réseau',
                'items': [
                    {'name': 'Interface Status', 'key': 'net.if.status', 'oid': '*******.*******.1.8'},
                    {'name': 'Interface Traffic In', 'key': 'net.if.in', 'oid': '*******.*******.1.10'},
                    {'name': 'Interface Traffic Out', 'key': 'net.if.out', 'oid': '*******.*******.1.16'}
                ]
            },
            {
                'name': 'Auto Discovery',
                'description': 'Template par défaut pour la découverte automatique',
                'items': [
                    {'name': 'System Name', 'key': 'system.name', 'oid': '*******.*******.0'},
                    {'name': 'System Description', 'key': 'system.descr', 'oid': '*******.*******.0'},
                    {'name': 'System Uptime', 'key': 'system.uptime', 'oid': '*******.*******.0'}
                ]
            }
        ]

        for template_data in default_templates:
            existing_template = Template.query.filter_by(name=template_data['name']).first()
            if not existing_template:
                new_template = Template(
                    name=template_data['name'],
                    description=template_data['description']
                )
                db.session.add(new_template)
                db.session.flush()  # Pour obtenir l'ID

                # Ajouter les items du template
                for item_data in template_data['items']:
                    new_item = Item(
                        name=item_data['name'],
                        key=item_data['key'],
                        oid=item_data['oid'],
                        template_id=new_template.id
                    )
                    db.session.add(new_item)

        # Créer une règle de découverte par défaut
        default_discovery_rule = DiscoveryRule.query.filter_by(name='Réseau Local').first()
        if not default_discovery_rule:
            # Obtenir le groupe et template par défaut
            auto_group = HostGroup.query.filter_by(name='Découverte Auto').first()
            auto_template = Template.query.filter_by(name='Auto Discovery').first()

            default_rule = DiscoveryRule(
                name='Réseau Local',
                network_range='***********/24',
                discovery_type='network',
                check_interval=3600,  # 1 heure
                enabled=False,  # Désactivé par défaut
                auto_add_hosts=True,
                default_host_group_id=auto_group.id if auto_group else None,
                default_template_id=auto_template.id if auto_template else None,
                snmp_community='public'
            )
            db.session.add(default_rule)
            print("[INFO] Règle de découverte par défaut créée (désactivée)")

        db.session.commit()
        print("[INFO] Base de données initialisée avec les données par défaut")

if __name__ == "__main__":
    init_database()
    # Pour accès réseau: host='0.0.0.0' permet l'accès depuis d'autres machines
    # Pour sécurité locale uniquement: host='127.0.0.1'
    app.run(debug=True, host='0.0.0.0', port=5006)

