#!/usr/bin/env python3
"""
Script de test pour le système de découverte automatique
"""

from app import app, db, DiscoveryRule, run_discovery_rule, run_all_discovery_rules
import sys

def test_discovery():
    """Test du système de découverte"""
    with app.app_context():
        print("=== Test du Système de Découverte ===\n")
        
        # Lister les règles existantes
        rules = DiscoveryRule.query.all()
        print(f"Règles de découverte trouvées: {len(rules)}")
        
        for rule in rules:
            print(f"- {rule.name}: {rule.network_range} (Actif: {rule.enabled})")
        
        if not rules:
            print("Aucune règle de découverte trouvée!")
            print("Créez une règle via l'interface web ou ajoutez-en une manuellement.")
            return
        
        # Demander à l'utilisateur quelle règle tester
        print("\nQuelle règle voulez-vous tester?")
        for i, rule in enumerate(rules, 1):
            print(f"{i}. {rule.name} ({rule.network_range})")
        
        try:
            choice = input("\nEntrez le numéro (ou 'all' pour toutes): ").strip()
            
            if choice.lower() == 'all':
                print("\n=== Exécution de toutes les règles ===")
                results = run_all_discovery_rules()
                for result in results:
                    print(f"\nRègle: {result['rule_name']}")
                    if result['result']['success']:
                        print(f"✅ {result['result']['message']}")
                    else:
                        print(f"❌ {result['result']['message']}")
            else:
                rule_index = int(choice) - 1
                if 0 <= rule_index < len(rules):
                    selected_rule = rules[rule_index]
                    print(f"\n=== Test de la règle: {selected_rule.name} ===")
                    print(f"Réseau: {selected_rule.network_range}")
                    print(f"Auto-ajout: {selected_rule.auto_add_hosts}")
                    
                    # Activer temporairement la règle si elle est désactivée
                    was_enabled = selected_rule.enabled
                    if not was_enabled:
                        print("⚠️  Règle désactivée - activation temporaire pour le test")
                        selected_rule.enabled = True
                        db.session.commit()
                    
                    # Exécuter la découverte
                    print("\n🔍 Début de la découverte...")
                    result = run_discovery_rule(selected_rule.id)
                    
                    # Restaurer l'état original
                    if not was_enabled:
                        selected_rule.enabled = False
                        db.session.commit()
                    
                    # Afficher les résultats
                    if result['success']:
                        print(f"✅ {result['message']}")
                        print(f"   - Hôtes découverts: {result.get('discovered_count', 0)}")
                        print(f"   - Nouveaux hôtes: {result.get('new_hosts_count', 0)}")
                    else:
                        print(f"❌ {result['message']}")
                else:
                    print("Choix invalide!")
                    
        except (ValueError, KeyboardInterrupt):
            print("\nTest annulé.")
            return
        
        print("\n=== Test terminé ===")

def create_test_rule():
    """Créer une règle de test"""
    with app.app_context():
        print("=== Création d'une règle de test ===\n")
        
        network = input("Réseau à scanner (ex: ***********/24): ").strip()
        if not network:
            network = "***********/24"
        
        try:
            import ipaddress
            ipaddress.ip_network(network, strict=False)
        except ValueError:
            print("Format de réseau invalide!")
            return
        
        # Créer la règle
        test_rule = DiscoveryRule(
            name=f"Test - {network}",
            network_range=network,
            discovery_type='network',
            check_interval=300,  # 5 minutes pour les tests
            enabled=True,
            auto_add_hosts=False,  # Pas d'auto-ajout pour les tests
            snmp_community='public'
        )
        
        db.session.add(test_rule)
        db.session.commit()
        
        print(f"✅ Règle de test créée: {test_rule.name}")
        print("Vous pouvez maintenant la tester avec l'option 1.")

def show_discovered_hosts():
    """Afficher les hôtes découverts"""
    from app import DiscoveredHost
    
    with app.app_context():
        hosts = DiscoveredHost.query.order_by(DiscoveredHost.last_seen.desc()).limit(20).all()
        
        print(f"=== Hôtes découverts ({len(hosts)}) ===\n")
        
        if not hosts:
            print("Aucun hôte découvert.")
            return
        
        for host in hosts:
            print(f"IP: {host.ip_address}")
            print(f"  Nom: {host.hostname or 'N/A'}")
            print(f"  Type: {host.device_type}")
            print(f"  Statut: {host.status}")
            print(f"  Découvert: {host.first_discovered.strftime('%d/%m/%Y %H:%M')}")
            print(f"  Dernière vue: {host.last_seen.strftime('%d/%m/%Y %H:%M')}")
            print(f"  Règle: {host.discovery_rule.name}")
            if host.services_detected and host.services_detected != '[]':
                print(f"  Services: {host.services_detected}")
            print()

def main():
    """Menu principal"""
    while True:
        print("\n" + "="*50)
        print("🔍 SYSTÈME DE DÉCOUVERTE - MENU DE TEST")
        print("="*50)
        print("1. Tester une règle de découverte")
        print("2. Créer une règle de test")
        print("3. Afficher les hôtes découverts")
        print("4. Quitter")
        print()
        
        choice = input("Votre choix: ").strip()
        
        if choice == '1':
            test_discovery()
        elif choice == '2':
            create_test_rule()
        elif choice == '3':
            show_discovered_hosts()
        elif choice == '4':
            print("Au revoir!")
            break
        else:
            print("Choix invalide!")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_discovery()
        elif sys.argv[1] == "create":
            create_test_rule()
        elif sys.argv[1] == "hosts":
            show_discovered_hosts()
        else:
            print("Usage: python test_discovery.py [test|create|hosts]")
    else:
        main()
