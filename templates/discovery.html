{% extends "base.html" %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
        <div class="card-body text-center py-5">
          <h1 class="display-4 fw-bold mb-3">
            <i class="bi bi-search me-3 pulse-animation"></i>Découverte Automatique
          </h1>
          <p class="lead mb-4 opacity-90">Détection et surveillance automatique des équipements réseau</p>
          <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="{{ url_for('discovery_rules') }}" class="btn btn-light btn-lg shadow">
              <i class="bi bi-gear me-2"></i>Gérer les Règles
            </a>
            <a href="{{ url_for('discovered_hosts') }}" class="btn btn-warning btn-lg shadow">
              <i class="bi bi-hdd-network me-2"></i>Hôtes Découverts
            </a>
            <button id="runAllDiscovery" class="btn btn-success btn-lg shadow">
              <i class="bi bi-play-circle me-2"></i>Lancer Toutes les Découvertes
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="row text-center g-4 mb-4">
    <div class="col-md-3">
      <div class="card shadow-sm border-primary">
        <div class="card-body">
          <h6>Règles Totales</h6>
          <h2 class="text-primary">{{ stats.total_rules }}</h2>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card shadow-sm border-success">
        <div class="card-body">
          <h6>Règles Actives</h6>
          <h2 class="text-success">{{ stats.active_rules }}</h2>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card shadow-sm border-info">
        <div class="card-body">
          <h6>Hôtes Découverts</h6>
          <h2 class="text-info">{{ stats.total_discovered }}</h2>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card shadow-sm border-warning">
        <div class="card-body">
          <h6>Nouveaux Aujourd'hui</h6>
          <h2 class="text-warning">{{ stats.new_today }}</h2>
        </div>
      </div>
    </div>
  </div>

  <div class="row gy-4">
    <!-- Règles de découverte -->
    <div class="col-md-6">
      <div class="card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Règles de Découverte</h5>
          <a href="{{ url_for('discovery_rules') }}" class="btn btn-sm btn-outline-primary">
            <i class="bi bi-plus-circle me-1"></i>Nouvelle Règle
          </a>
        </div>
        <div class="card-body">
          {% if rules %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Nom</th>
                    <th>Réseau</th>
                    <th>État</th>
                    <th>Dernière Exécution</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for rule in rules %}
                  <tr>
                    <td>{{ rule.name }}</td>
                    <td><code>{{ rule.network_range }}</code></td>
                    <td>
                      {% if rule.enabled %}
                        <span class="badge bg-success">Actif</span>
                      {% else %}
                        <span class="badge bg-secondary">Inactif</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if rule.last_discovery %}
                        {{ rule.last_discovery.strftime('%d/%m %H:%M') }}
                      {% else %}
                        <span class="text-muted">Jamais</span>
                      {% endif %}
                    </td>
                    <td>
                      <form method="post" action="{{ url_for('run_discovery_rule_manual', rule_id=rule.id) }}" style="display: inline;">
                        <button type="submit" class="btn btn-sm btn-outline-success" title="Exécuter maintenant">
                          <i class="bi bi-play-circle"></i>
                        </button>
                      </form>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-4">
              <i class="bi bi-search display-1 text-muted"></i>
              <p class="text-muted mt-3">Aucune règle de découverte configurée</p>
              <a href="{{ url_for('discovery_rules') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>Créer la première règle
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Événements récents -->
    <div class="col-md-6">
      <div class="card shadow">
        <div class="card-header">
          <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>Événements Récents</h5>
        </div>
        <div class="card-body">
          {% if recent_events %}
            <div class="timeline">
              {% for event in recent_events %}
              <div class="timeline-item">
                <div class="timeline-marker bg-{{ 'success' if event.event_type == 'host_discovered' else 'info' if event.event_type == 'host_added' else 'warning' }}"></div>
                <div class="timeline-content">
                  <h6 class="mb-1">
                    {% if event.event_type == 'host_discovered' %}
                      <i class="bi bi-plus-circle text-success"></i> Hôte découvert
                    {% elif event.event_type == 'host_added' %}
                      <i class="bi bi-check-circle text-info"></i> Hôte ajouté
                    {% else %}
                      <i class="bi bi-exclamation-circle text-warning"></i> {{ event.event_type }}
                    {% endif %}
                  </h6>
                  <p class="mb-1">
                    <strong>{{ event.ip_address }}</strong>
                    {% if event.hostname %} ({{ event.hostname }}) {% endif %}
                  </p>
                  <small class="text-muted">
                    {{ event.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}
                    • {{ event.discovery_rule.name }}
                  </small>
                </div>
              </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-4">
              <i class="bi bi-clock-history display-1 text-muted"></i>
              <p class="text-muted mt-3">Aucun événement récent</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Hôtes découverts non ajoutés -->
  {% if discovered_hosts %}
  <div class="row mt-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="bi bi-hdd-network me-2"></i>Hôtes Découverts (En Attente)</h5>
          <a href="{{ url_for('discovered_hosts') }}" class="btn btn-sm btn-outline-primary">Voir Tous</a>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>IP</th>
                  <th>Nom</th>
                  <th>Type</th>
                  <th>Découvert le</th>
                  <th>Règle</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for host in discovered_hosts[:10] %}
                <tr>
                  <td><code>{{ host.ip_address }}</code></td>
                  <td>{{ host.hostname or 'N/A' }}</td>
                  <td>
                    <span class="badge bg-secondary">{{ host.device_type }}</span>
                  </td>
                  <td>{{ host.first_discovered.strftime('%d/%m %H:%M') }}</td>
                  <td>{{ host.discovery_rule.name }}</td>
                  <td>
                    <form method="post" action="{{ url_for('add_discovered_host_manual', host_id=host.id) }}" style="display: inline;">
                      <button type="submit" class="btn btn-sm btn-success" title="Ajouter à la surveillance">
                        <i class="bi bi-plus-circle"></i>
                      </button>
                    </form>
                    <form method="post" action="{{ url_for('ignore_discovered_host', host_id=host.id) }}" style="display: inline;">
                      <button type="submit" class="btn btn-sm btn-outline-secondary" title="Ignorer">
                        <i class="bi bi-x-circle"></i>
                      </button>
                    </form>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<style>
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-marker {
  position: absolute;
  left: -35px;
  top: 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -30px;
  top: 17px;
  width: 2px;
  height: calc(100% + 5px);
  background-color: #dee2e6;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
</style>

<script>
document.getElementById('runAllDiscovery').addEventListener('click', function() {
  this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Exécution...';
  this.disabled = true;
  
  fetch('/api/discovery/run-all', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      location.reload();
    } else {
      alert('Erreur lors de l\'exécution');
      this.innerHTML = '<i class="bi bi-play-circle me-2"></i>Lancer Toutes les Découvertes';
      this.disabled = false;
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Erreur de connexion');
    this.innerHTML = '<i class="bi bi-play-circle me-2"></i>Lancer Toutes les Découvertes';
    this.disabled = false;
  });
});
</script>
{% endblock %}
