{% extends "base.html" %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; border: none;">
        <div class="card-body text-center py-4">
          <h1 class="display-5 fw-bold mb-3">
            <i class="bi bi-gear me-3"></i>Règles de Découverte
          </h1>
          <p class="lead mb-3 opacity-90">Configuration des règles de découverte automatique</p>
          <a href="{{ url_for('discovery_dashboard') }}" class="btn btn-light btn-lg shadow">
            <i class="bi bi-arrow-left me-2"></i>Retour au Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Formulaire de création -->
    <div class="col-md-4">
      <div class="card shadow">
        <div class="card-header">
          <h5 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Nouvelle Règle</h5>
        </div>
        <div class="card-body">
          <form method="post">
            <div class="mb-3">
              <label for="name" class="form-label">Nom de la règle</label>
              <input type="text" class="form-control" id="name" name="name" required
                     placeholder="Ex: Réseau Bureau">
            </div>

            <div class="mb-3">
              <label for="network_range" class="form-label">Plage réseau (CIDR)</label>
              <input type="text" class="form-control" id="network_range" name="network_range" required
                     placeholder="Ex: ***********/24">
              <div class="form-text">Format CIDR (ex: ***********/24)</div>
            </div>

            <div class="mb-3">
              <label for="check_interval" class="form-label">Intervalle de vérification</label>
              <select class="form-select" id="check_interval" name="check_interval">
                <option value="1800">30 minutes</option>
                <option value="3600" selected>1 heure</option>
                <option value="7200">2 heures</option>
                <option value="21600">6 heures</option>
                <option value="43200">12 heures</option>
                <option value="86400">24 heures</option>
              </select>
            </div>

            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="auto_add_hosts" name="auto_add_hosts" checked>
                <label class="form-check-label" for="auto_add_hosts">
                  Ajouter automatiquement les nouveaux hôtes
                </label>
              </div>
            </div>

            <div class="mb-3">
              <label for="default_host_group_id" class="form-label">Groupe d'hôtes par défaut</label>
              <select class="form-select" id="default_host_group_id" name="default_host_group_id">
                <option value="">Aucun groupe</option>
                {% for group in host_groups %}
                <option value="{{ group.id }}">{{ group.name }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="mb-3">
              <label for="default_template_id" class="form-label">Template par défaut</label>
              <select class="form-select" id="default_template_id" name="default_template_id">
                <option value="">Aucun template</option>
                {% for template in templates %}
                <option value="{{ template.id }}">{{ template.name }}</option>
                {% endfor %}
              </select>
            </div>

            <div class="mb-3">
              <label for="snmp_community" class="form-label">Communauté SNMP</label>
              <input type="text" class="form-control" id="snmp_community" name="snmp_community" 
                     value="public" placeholder="public">
            </div>

            <button type="submit" class="btn btn-success w-100">
              <i class="bi bi-plus-circle me-2"></i>Créer la Règle
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Liste des règles existantes -->
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-header">
          <h5 class="mb-0"><i class="bi bi-list-check me-2"></i>Règles Existantes</h5>
        </div>
        <div class="card-body">
          {% if rules %}
            <div class="table-responsive">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Nom</th>
                    <th>Réseau</th>
                    <th>Intervalle</th>
                    <th>État</th>
                    <th>Auto-ajout</th>
                    <th>Dernière Exécution</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for rule in rules %}
                  <tr>
                    <td>
                      <strong>{{ rule.name }}</strong>
                      {% if rule.default_host_group %}
                        <br><small class="text-muted">Groupe: {{ rule.default_host_group.name }}</small>
                      {% endif %}
                    </td>
                    <td><code>{{ rule.network_range }}</code></td>
                    <td>
                      {% set hours = rule.check_interval // 3600 %}
                      {% set minutes = (rule.check_interval % 3600) // 60 %}
                      {% if hours > 0 %}
                        {{ hours }}h
                        {% if minutes > 0 %}{{ minutes }}m{% endif %}
                      {% else %}
                        {{ minutes }}m
                      {% endif %}
                    </td>
                    <td>
                      {% if rule.enabled %}
                        <span class="badge bg-success">Actif</span>
                      {% else %}
                        <span class="badge bg-secondary">Inactif</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if rule.auto_add_hosts %}
                        <i class="bi bi-check-circle text-success" title="Auto-ajout activé"></i>
                      {% else %}
                        <i class="bi bi-x-circle text-muted" title="Auto-ajout désactivé"></i>
                      {% endif %}
                    </td>
                    <td>
                      {% if rule.last_discovery %}
                        {{ rule.last_discovery.strftime('%d/%m/%Y %H:%M') }}
                      {% else %}
                        <span class="text-muted">Jamais</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <!-- Exécuter -->
                        <form method="post" action="{{ url_for('run_discovery_rule_manual', rule_id=rule.id) }}" style="display: inline;">
                          <button type="submit" class="btn btn-sm btn-outline-success" title="Exécuter maintenant">
                            <i class="bi bi-play-circle"></i>
                          </button>
                        </form>
                        
                        <!-- Activer/Désactiver -->
                        <form method="post" action="{{ url_for('toggle_discovery_rule', rule_id=rule.id) }}" style="display: inline;">
                          <button type="submit" class="btn btn-sm btn-outline-{{ 'warning' if rule.enabled else 'success' }}" 
                                  title="{{ 'Désactiver' if rule.enabled else 'Activer' }}">
                            <i class="bi bi-{{ 'pause' if rule.enabled else 'play' }}-circle"></i>
                          </button>
                        </form>
                        
                        <!-- Supprimer -->
                        <form method="post" action="{{ url_for('delete_discovery_rule', rule_id=rule.id) }}" 
                              style="display: inline;" 
                              onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette règle ?')">
                          <button type="submit" class="btn btn-sm btn-outline-danger" title="Supprimer">
                            <i class="bi bi-trash"></i>
                          </button>
                        </form>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="bi bi-search display-1 text-muted"></i>
              <h4 class="text-muted mt-3">Aucune règle de découverte</h4>
              <p class="text-muted">Créez votre première règle pour commencer la découverte automatique</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Aide et exemples -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header">
          <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>Aide et Exemples</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6><i class="bi bi-lightbulb me-2"></i>Comment ça fonctionne</h6>
              <ul class="list-unstyled">
                <li><i class="bi bi-check text-success me-2"></i>Les règles scannent automatiquement les réseaux définis</li>
                <li><i class="bi bi-check text-success me-2"></i>Détection via PING et SNMP des nouveaux équipements</li>
                <li><i class="bi bi-check text-success me-2"></i>Identification automatique du type d'équipement</li>
                <li><i class="bi bi-check text-success me-2"></i>Ajout automatique à la surveillance (optionnel)</li>
                <li><i class="bi bi-check text-success me-2"></i>Génération d'alertes pour les nouveaux hôtes</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6><i class="bi bi-diagram-3 me-2"></i>Exemples de réseaux</h6>
              <div class="d-flex flex-wrap gap-2">
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="***********/24">
                  ***********/24 <small>(Réseau domestique)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="**********/16">
                  **********/16 <small>(Réseau entreprise)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="10.0.0.0/8">
                  10.0.0.0/8 <small>(Réseau privé large)</small>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Remplir automatiquement le champ réseau avec les exemples
document.querySelectorAll('.example-network').forEach(button => {
  button.addEventListener('click', function() {
    const network = this.dataset.network;
    document.getElementById('network_range').value = network;
  });
});
</script>
{% endblock %}
