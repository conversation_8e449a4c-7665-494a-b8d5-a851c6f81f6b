{% extends "base.html" %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none;">
        <div class="card-body text-center py-4">
          <h1 class="display-5 fw-bold mb-3">
            <i class="bi bi-hdd-network me-3"></i>Hôtes Découverts
          </h1>
          <p class="lead mb-3 opacity-90">Équipements détectés automatiquement sur le réseau</p>
          <a href="{{ url_for('discovery_dashboard') }}" class="btn btn-light btn-lg shadow">
            <i class="bi bi-arrow-left me-2"></i>Retour au Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-body">
          <div class="row align-items-end">
            <div class="col-md-3">
              <label for="statusFilter" class="form-label">Filtrer par statut</label>
              <select class="form-select" id="statusFilter">
                <option value="">Tous les statuts</option>
                <option value="discovered">Découvert</option>
                <option value="added">Ajouté</option>
                <option value="ignored">Ignoré</option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="deviceTypeFilter" class="form-label">Type d'équipement</label>
              <select class="form-select" id="deviceTypeFilter">
                <option value="">Tous les types</option>
                <option value="router">Routeur</option>
                <option value="switch">Switch</option>
                <option value="server">Serveur</option>
                <option value="workstation">Poste de travail</option>
                <option value="unknown">Inconnu</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="searchInput" class="form-label">Rechercher</label>
              <input type="text" class="form-control" id="searchInput" placeholder="IP, nom d'hôte...">
            </div>
            <div class="col-md-2">
              <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                <i class="bi bi-x-circle me-1"></i>Effacer
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Liste des hôtes découverts -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0"><i class="bi bi-list me-2"></i>Hôtes Découverts (<span id="hostCount">{{ hosts|length }}</span>)</h5>
          <div>
            <button class="btn btn-sm btn-success" onclick="addSelectedHosts()" id="addSelectedBtn" disabled>
              <i class="bi bi-plus-circle me-1"></i>Ajouter Sélectionnés
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="ignoreSelectedHosts()" id="ignoreSelectedBtn" disabled>
              <i class="bi bi-x-circle me-1"></i>Ignorer Sélectionnés
            </button>
          </div>
        </div>
        <div class="card-body">
          {% if hosts %}
            <div class="table-responsive">
              <table class="table table-hover" id="hostsTable">
                <thead>
                  <tr>
                    <th>
                      <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    </th>
                    <th>IP</th>
                    <th>Nom d'hôte</th>
                    <th>Type</th>
                    <th>Système</th>
                    <th>Services</th>
                    <th>Découvert le</th>
                    <th>Dernière vue</th>
                    <th>Règle</th>
                    <th>Statut</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for host in hosts %}
                  <tr data-status="{{ host.status }}" data-device-type="{{ host.device_type }}" data-search="{{ host.ip_address }} {{ host.hostname or '' }}">
                    <td>
                      {% if host.status == 'discovered' %}
                        <input type="checkbox" class="host-checkbox" value="{{ host.id }}">
                      {% endif %}
                    </td>
                    <td>
                      <code>{{ host.ip_address }}</code>
                    </td>
                    <td>
                      {% if host.hostname %}
                        <strong>{{ host.hostname }}</strong>
                        {% if host.snmp_sysname and host.snmp_sysname != host.hostname %}
                          <br><small class="text-muted">SNMP: {{ host.snmp_sysname }}</small>
                        {% endif %}
                      {% else %}
                        <span class="text-muted">N/A</span>
                      {% endif %}
                    </td>
                    <td>
                      <span class="badge bg-{{ 'primary' if host.device_type == 'router' else 'info' if host.device_type == 'switch' else 'success' if host.device_type == 'server' else 'warning' if host.device_type == 'workstation' else 'secondary' }}">
                        {{ host.device_type.title() }}
                      </span>
                    </td>
                    <td>
                      {% if host.os_info %}
                        <small title="{{ host.os_info }}">{{ host.os_info[:30] }}{% if host.os_info|length > 30 %}...{% endif %}</small>
                      {% else %}
                        <span class="text-muted">N/A</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if host.services_detected and host.services_detected != '[]' %}
                        {% set services = host.services_detected|replace("'", '"')|from_json %}
                        {% for service in services[:3] %}
                          <span class="badge bg-light text-dark me-1">{{ service.service }}</span>
                        {% endfor %}
                        {% if services|length > 3 %}
                          <span class="badge bg-secondary">+{{ services|length - 3 }}</span>
                        {% endif %}
                      {% else %}
                        <span class="text-muted">Aucun</span>
                      {% endif %}
                    </td>
                    <td>{{ host.first_discovered.strftime('%d/%m/%Y %H:%M') }}</td>
                    <td>{{ host.last_seen.strftime('%d/%m/%Y %H:%M') }}</td>
                    <td>
                      <small>{{ host.discovery_rule.name }}</small>
                    </td>
                    <td>
                      {% if host.status == 'discovered' %}
                        <span class="badge bg-warning">En attente</span>
                      {% elif host.status == 'added' %}
                        <span class="badge bg-success">Ajouté</span>
                      {% elif host.status == 'ignored' %}
                        <span class="badge bg-secondary">Ignoré</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if host.status == 'discovered' %}
                        <div class="btn-group" role="group">
                          <form method="post" action="{{ url_for('add_discovered_host_manual', host_id=host.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-sm btn-success" title="Ajouter à la surveillance">
                              <i class="bi bi-plus-circle"></i>
                            </button>
                          </form>
                          <form method="post" action="{{ url_for('ignore_discovered_host', host_id=host.id) }}" style="display: inline;">
                            <button type="submit" class="btn btn-sm btn-outline-secondary" title="Ignorer">
                              <i class="bi bi-x-circle"></i>
                            </button>
                          </form>
                        </div>
                      {% elif host.status == 'added' and host.machine %}
                        <a href="{{ url_for('machine_detail', ip=host.machine.ip) }}" class="btn btn-sm btn-outline-primary" title="Voir les détails">
                          <i class="bi bi-eye"></i>
                        </a>
                      {% else %}
                        <span class="text-muted">-</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="bi bi-hdd-network display-1 text-muted"></i>
              <h4 class="text-muted mt-3">Aucun hôte découvert</h4>
              <p class="text-muted">Configurez des règles de découverte pour détecter automatiquement les équipements</p>
              <a href="{{ url_for('discovery_rules') }}" class="btn btn-primary">
                <i class="bi bi-gear me-2"></i>Configurer les Règles
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Filtrage et recherche
function applyFilters() {
  const statusFilter = document.getElementById('statusFilter').value;
  const deviceTypeFilter = document.getElementById('deviceTypeFilter').value;
  const searchInput = document.getElementById('searchInput').value.toLowerCase();
  
  const rows = document.querySelectorAll('#hostsTable tbody tr');
  let visibleCount = 0;
  
  rows.forEach(row => {
    const status = row.dataset.status;
    const deviceType = row.dataset.deviceType;
    const searchText = row.dataset.search.toLowerCase();
    
    let show = true;
    
    if (statusFilter && status !== statusFilter) show = false;
    if (deviceTypeFilter && deviceType !== deviceTypeFilter) show = false;
    if (searchInput && !searchText.includes(searchInput)) show = false;
    
    row.style.display = show ? '' : 'none';
    if (show) visibleCount++;
  });
  
  document.getElementById('hostCount').textContent = visibleCount;
}

function clearFilters() {
  document.getElementById('statusFilter').value = '';
  document.getElementById('deviceTypeFilter').value = '';
  document.getElementById('searchInput').value = '';
  applyFilters();
}

// Sélection multiple
function toggleSelectAll() {
  const selectAll = document.getElementById('selectAll');
  const checkboxes = document.querySelectorAll('.host-checkbox');
  
  checkboxes.forEach(checkbox => {
    const row = checkbox.closest('tr');
    if (row.style.display !== 'none') {
      checkbox.checked = selectAll.checked;
    }
  });
  
  updateActionButtons();
}

function updateActionButtons() {
  const checkedBoxes = document.querySelectorAll('.host-checkbox:checked');
  const addBtn = document.getElementById('addSelectedBtn');
  const ignoreBtn = document.getElementById('ignoreSelectedBtn');
  
  const hasSelection = checkedBoxes.length > 0;
  addBtn.disabled = !hasSelection;
  ignoreBtn.disabled = !hasSelection;
}

function addSelectedHosts() {
  const checkedBoxes = document.querySelectorAll('.host-checkbox:checked');
  if (checkedBoxes.length === 0) return;
  
  if (confirm(`Ajouter ${checkedBoxes.length} hôte(s) à la surveillance ?`)) {
    checkedBoxes.forEach(checkbox => {
      const hostId = checkbox.value;
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/discovery/host/${hostId}/add`;
      document.body.appendChild(form);
      form.submit();
    });
  }
}

function ignoreSelectedHosts() {
  const checkedBoxes = document.querySelectorAll('.host-checkbox:checked');
  if (checkedBoxes.length === 0) return;
  
  if (confirm(`Ignorer ${checkedBoxes.length} hôte(s) ?`)) {
    checkedBoxes.forEach(checkbox => {
      const hostId = checkbox.value;
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/discovery/host/${hostId}/ignore`;
      document.body.appendChild(form);
      form.submit();
    });
  }
}

// Event listeners
document.getElementById('statusFilter').addEventListener('change', applyFilters);
document.getElementById('deviceTypeFilter').addEventListener('change', applyFilters);
document.getElementById('searchInput').addEventListener('input', applyFilters);

document.querySelectorAll('.host-checkbox').forEach(checkbox => {
  checkbox.addEventListener('change', updateActionButtons);
});

// Initialisation
applyFilters();
</script>
{% endblock %}
