{% extends "base.html" %}

{% block head %}
  {{ super() }}
  {# Ici aucune balise favicon #}
{% endblock %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; border: none;">
        <div class="card-body text-center py-5">
          <h1 class="display-4 fw-bold mb-3">
            <i class="bi bi-radar me-3 pulse-animation"></i><PERSON>anne<PERSON>
          </h1>
          <p class="lead mb-4 opacity-90">Découverte et analyse automatique des équipements réseau</p>
          <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg shadow">
            <i class="bi bi-speedometer2 me-2"></i>Retour au Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire de scan moderne -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="bi bi-gear me-2"></i>Configuration du Scan
          </h5>
        </div>
        <div class="card-body">
          <form method="post">
            <div class="row align-items-end">
              <div class="col-lg-6 col-md-8 mb-3">
                <label for="network_cidr" class="form-label fw-semibold">
                  <i class="bi bi-diagram-3 me-2"></i>Plage IP à scanner
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="bi bi-router"></i>
                  </span>
                  <input type="text" class="form-control" id="network_cidr" name="network_cidr"
                         placeholder="Ex: ***********/24" value="{{ ip_input }}" required>
                </div>
                <div class="form-text">
                  <i class="bi bi-info-circle me-1"></i>
                  Utilisez la notation CIDR (ex: ***********/24 pour 254 adresses)
                </div>
              </div>

              <div class="col-lg-3 col-md-4 mb-3">
                <button type="submit" class="btn btn-success btn-lg w-100">
                  <i class="bi bi-play-circle me-2"></i>Lancer le Scan
                </button>
              </div>

              <div class="col-lg-3 col-md-12 mb-3">
                <a href="{{ url_for('historique') }}" class="btn btn-outline-primary btn-lg w-100">
                  <i class="bi bi-clock-history me-2"></i>Historique
                </a>
              </div>
            </div>

            <!-- Exemples de réseaux -->
            <div class="mt-3">
              <small class="text-muted fw-semibold">Exemples courants :</small>
              <div class="d-flex flex-wrap gap-2 mt-2">
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="***********/24">
                  ***********/24 <small>(Réseau domestique)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="172.16.0.0/24">
                  172.16.0.0/24 <small>(Réseau entreprise)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="10.0.0.0/24">
                  10.0.0.0/24 <small>(Réseau privé)</small>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  {% if error %}
  <div class="alert alert-danger">{{ error }}</div>
  {% endif %}

  {% if alerts %}
  <div class="alert alert-warning">
    <h5>⚠️ Alertes :</h5>
    <ul>
      {% for alert in alerts %}
      <li>{{ alert }}</li>
      {% endfor %}
    </ul>
  </div>
  {% endif %}

  {% if results %}
  <!-- Info sur les résultats -->
  <div class="alert alert-info mb-3">
    <i class="bi bi-info-circle me-2"></i>
    <strong>{{ results|length }} machines détectées</strong> sur le réseau {{ ip_input }}.
    Les machines sans SNMP configuré apparaissent comme "Machine détectée".
    <br><small class="text-muted">
      💡 Pour obtenir plus d'informations (nom, CPU, mémoire), configurez SNMP sur vos machines.
    </small>
  </div>

  <!-- Filtres et Recherche Avancés -->
  <div class="card mb-3">
    <div class="card-header">
      <h6 class="mb-0"><i class="bi bi-funnel me-2"></i>Filtres et Recherche</h6>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <!-- Recherche par IP/Nom -->
        <div class="col-md-4">
          <label for="searchInput" class="form-label">
            <i class="bi bi-search me-1"></i>Rechercher
          </label>
          <input type="text" id="searchInput" class="form-control"
                 placeholder="IP, nom, description..."
                 title="Rechercher par IP, nom ou description">
        </div>

        <!-- Filtre par Statut -->
        <div class="col-md-2">
          <label for="statusFilter" class="form-label">
            <i class="bi bi-activity me-1"></i>Statut
          </label>
          <select id="statusFilter" class="form-select">
            <option value="all">Tous</option>
            <option value="up">En ligne</option>
            <option value="down">Hors ligne</option>
          </select>
        </div>

        <!-- Filtre par CPU -->
        <div class="col-md-2">
          <label for="cpuFilter" class="form-label">
            <i class="bi bi-cpu me-1"></i>CPU
          </label>
          <select id="cpuFilter" class="form-select">
            <option value="all">Tous</option>
            <option value="high">≥ 80%</option>
            <option value="medium">50-79%</option>
            <option value="low">< 50%</option>
            <option value="na">N/A</option>
          </select>
        </div>

        <!-- Filtre par Mémoire -->
        <div class="col-md-2">
          <label for="memoryFilter" class="form-label">
            <i class="bi bi-memory me-1"></i>Mémoire
          </label>
          <select id="memoryFilter" class="form-select">
            <option value="all">Tous</option>
            <option value="high">≥ 80%</option>
            <option value="medium">50-79%</option>
            <option value="low">< 50%</option>
            <option value="na">N/A</option>
          </select>
        </div>

        <!-- Nombre de lignes -->
        <div class="col-md-2">
          <label for="rowsCount" class="form-label">
            <i class="bi bi-list-ol me-1"></i>Afficher
          </label>
          <select id="rowsCount" class="form-select">
            <option>5</option>
            <option selected>10</option>
            <option>25</option>
            <option>50</option>
            <option>100</option>
            <option value="all">Tous</option>
          </select>
        </div>
      </div>

      <!-- Boutons d'action -->
      <div class="row mt-3">
        <div class="col-12">
          <button id="clearFilters" class="btn btn-outline-secondary btn-sm me-2">
            <i class="bi bi-x-circle me-1"></i>Effacer les filtres
          </button>
          <button id="exportResults" class="btn btn-outline-primary btn-sm me-2">
            <i class="bi bi-download me-1"></i>Exporter (CSV)
          </button>
          <span id="resultsCount" class="badge bg-info ms-2">0 résultats</span>
        </div>
      </div>
    </div>
  </div>

  <table id="resultsTable" class="table table-bordered table-striped table-hover">
    <thead class="table-dark">
      <tr>
        <th>Adresse IP</th>
        <th>Nom (SNMP)</th>
        <th>État</th>
        <th>CPU (%)</th>
        <th>Mémoire (%)</th>
        <th>Disque (%)</th>
        <th>Uptime</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {% for machine in results %}
      {% set status = machine['status'] if machine['status'] else machine.status %}
      {% set name = machine['name'] if machine['name'] else machine.name %}
      {% set description = machine['description'] if machine['description'] else machine.description %}
      {% set cpu = machine['cpu_load'] if machine['cpu_load'] else machine.cpu_load %}
      {% set memory = machine['memory_usage'] if machine['memory_usage'] else machine.memory_usage %}
      {% set ip = machine['ip'] if machine['ip'] else machine.ip %}

      <tr data-status="{{ status }}"
          data-ip="{{ ip }}"
          data-name="{{ name if name and name != 'N/A' else '' }}"
          data-description="{{ description if description and description != 'N/A' else '' }}"
          data-cpu="{{ cpu if cpu is not none else 'na' }}"
          data-memory="{{ memory if memory is not none else 'na' }}"
          data-search="{{ ip }} {{ name if name and name != 'N/A' else 'Machine détectée' }} {{ description if description and description != 'N/A' else '' }}">
        <td><a href="{{ url_for('machine_detail', ip=machine['ip'] if machine['ip'] else machine.ip) }}">{{ machine['ip'] if machine['ip'] else machine.ip }}</a></td>
        <td>
          {% set name = machine['name'] if machine['name'] else machine.name %}
          {% if name and name != 'N/A' %}
            {{ name }}
          {% else %}
            <span class="text-muted">
              <i class="bi bi-question-circle" title="Machine détectée sans SNMP"></i>
              Machine détectée
            </span>
          {% endif %}
        </td>
        <td>
          {% set status = machine['status'] if machine['status'] else machine.status %}
          {% if status == "up" %}
          <span class="badge bg-success">En ligne</span>
          {% else %}
          <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
        <td>
          {% set cpu = machine['cpu_load'] if machine['cpu_load'] else machine.cpu_load %}
          {% if cpu is not none %}
            <span class="badge {% if cpu > 80 %}bg-danger{% elif cpu > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ cpu }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set memory = machine['memory_usage'] if machine['memory_usage'] else machine.memory_usage %}
          {% if memory is not none %}
            <span class="badge {% if memory > 80 %}bg-danger{% elif memory > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ memory }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set disk = machine['disk_usage'] if machine['disk_usage'] else machine.disk_usage %}
          {% if disk is not none %}
            <span class="badge {% if disk > 80 %}bg-danger{% elif disk > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ disk }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set uptime = machine['uptime'] if machine['uptime'] else machine.uptime %}
          {% if uptime is not none %}
            {% set days = (uptime // 86400) %}
            {% set hours = ((uptime % 86400) // 3600) %}
            {% set minutes = ((uptime % 3600) // 60) %}
            <small>{{ days }}d {{ hours }}h {{ minutes }}m</small>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set desc = machine['description'] if machine['description'] else machine.description %}
          <small class="text-muted">{{ desc[:50] + '...' if desc and desc|length > 50 else desc if desc else 'N/A' }}</small>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% endif %}
</div>

<!-- JavaScript pour Filtrage et Recherche Avancés -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('resultsTable');
    const tbody = table.querySelector('tbody');
    const allRows = Array.from(tbody.querySelectorAll('tr'));

    // Éléments de filtrage
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const cpuFilter = document.getElementById('cpuFilter');
    const memoryFilter = document.getElementById('memoryFilter');
    const rowsCount = document.getElementById('rowsCount');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const exportBtn = document.getElementById('exportResults');
    const resultsCount = document.getElementById('resultsCount');

    let currentPage = 1;
    let filteredRows = [...allRows];

    // Fonction principale de filtrage
    function applyFilters() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const statusValue = statusFilter.value;
        const cpuValue = cpuFilter.value;
        const memoryValue = memoryFilter.value;

        filteredRows = allRows.filter(row => {
            // Recherche textuelle
            if (searchTerm) {
                const searchData = row.dataset.search.toLowerCase();
                if (!searchData.includes(searchTerm)) {
                    return false;
                }
            }

            // Filtre par statut
            if (statusValue !== 'all') {
                if (row.dataset.status !== statusValue) {
                    return false;
                }
            }

            // Filtre par CPU
            if (cpuValue !== 'all') {
                const cpu = row.dataset.cpu;
                if (cpuValue === 'na' && cpu !== 'na') return false;
                if (cpuValue === 'high' && (cpu === 'na' || parseInt(cpu) < 80)) return false;
                if (cpuValue === 'medium' && (cpu === 'na' || parseInt(cpu) < 50 || parseInt(cpu) >= 80)) return false;
                if (cpuValue === 'low' && (cpu === 'na' || parseInt(cpu) >= 50)) return false;
            }

            // Filtre par mémoire
            if (memoryValue !== 'all') {
                const memory = row.dataset.memory;
                if (memoryValue === 'na' && memory !== 'na') return false;
                if (memoryValue === 'high' && (memory === 'na' || parseInt(memory) < 80)) return false;
                if (memoryValue === 'medium' && (memory === 'na' || parseInt(memory) < 50 || parseInt(memory) >= 80)) return false;
                if (memoryValue === 'low' && (memory === 'na' || parseInt(memory) >= 50)) return false;
            }

            return true;
        });

        updateDisplay();
    }

    // Mise à jour de l'affichage
    function updateDisplay() {
        // Cacher toutes les lignes
        allRows.forEach(row => row.style.display = 'none');

        // Afficher les lignes filtrées selon la pagination
        const rowsPerPage = rowsCount.value === 'all' ? filteredRows.length : parseInt(rowsCount.value);
        const startIndex = (currentPage - 1) * rowsPerPage;
        const endIndex = startIndex + rowsPerPage;

        const visibleRows = filteredRows.slice(startIndex, endIndex);
        visibleRows.forEach(row => row.style.display = '');

        // Mettre à jour le compteur
        resultsCount.textContent = `${filteredRows.length} résultat${filteredRows.length > 1 ? 's' : ''}`;

        // Mettre à jour la pagination (simple)
        updatePagination();
    }

    // Pagination simple
    function updatePagination() {
        const rowsPerPage = rowsCount.value === 'all' ? filteredRows.length : parseInt(rowsCount.value);
        const totalPages = Math.ceil(filteredRows.length / rowsPerPage);

        // Réinitialiser la page si nécessaire
        if (currentPage > totalPages) {
            currentPage = Math.max(1, totalPages);
        }
    }

    // Effacer tous les filtres
    function clearFilters() {
        searchInput.value = '';
        statusFilter.value = 'all';
        cpuFilter.value = 'all';
        memoryFilter.value = 'all';
        rowsCount.value = '10';
        currentPage = 1;
        applyFilters();
    }

    // Exporter en CSV
    function exportToCSV() {
        const headers = ['IP', 'Nom', 'Statut', 'CPU (%)', 'Mémoire (%)', 'Disque (%)', 'Uptime', 'Description'];
        let csvContent = headers.join(',') + '\\n';

        filteredRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const rowData = Array.from(cells).map(cell => {
                let text = cell.textContent.trim();
                // Nettoyer le texte pour CSV
                text = text.replace(/"/g, '""');
                return `"${text}"`;
            });
            csvContent += rowData.join(',') + '\\n';
        });

        // Télécharger le fichier
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `scan_results_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Event listeners
    searchInput.addEventListener('input', applyFilters);
    statusFilter.addEventListener('change', applyFilters);
    cpuFilter.addEventListener('change', applyFilters);
    memoryFilter.addEventListener('change', applyFilters);
    rowsCount.addEventListener('change', () => {
        currentPage = 1;
        applyFilters();
    });
    clearFiltersBtn.addEventListener('click', clearFilters);
    exportBtn.addEventListener('click', exportToCSV);

    // Raccourcis clavier
    document.addEventListener('keydown', function(e) {
        // Ctrl+F pour focus sur la recherche
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
        }
        // Escape pour effacer les filtres
        if (e.key === 'Escape') {
            clearFilters();
        }
    });

    // Initialisation
    applyFilters();
});

// Remplir automatiquement le champ réseau avec les exemples
document.querySelectorAll('.example-network').forEach(button => {
    button.addEventListener('click', function() {
        const network = this.dataset.network;
        document.getElementById('network_cidr').value = network;
    });
});
</script>

<style>
/* Styles pour améliorer l'apparence */
.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#searchInput:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.badge {
    font-size: 0.75em;
    padding: 0.35em 0.65em;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Responsive */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.7em;
    }
}
</style>
{% endblock %}
