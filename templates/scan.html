{% extends "base.html" %}

{% block head %}
  {{ super() }}
  {# Ici aucune balise favicon #}
{% endblock %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); color: white; border: none;">
        <div class="card-body text-center py-5">
          <h1 class="display-4 fw-bold mb-3">
            <i class="bi bi-radar me-3 pulse-animation"></i><PERSON>anne<PERSON>
          </h1>
          <p class="lead mb-4 opacity-90">Découverte et analyse automatique des équipements réseau</p>
          <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg shadow">
            <i class="bi bi-speedometer2 me-2"></i>Retour au Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire de scan moderne -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="bi bi-gear me-2"></i>Configuration du Scan
          </h5>
        </div>
        <div class="card-body">
          <form method="post">
            <div class="row align-items-end">
              <div class="col-lg-6 col-md-8 mb-3">
                <label for="network_cidr" class="form-label fw-semibold">
                  <i class="bi bi-diagram-3 me-2"></i>Plage IP à scanner
                </label>
                <div class="input-group">
                  <span class="input-group-text">
                    <i class="bi bi-router"></i>
                  </span>
                  <input type="text" class="form-control" id="network_cidr" name="network_cidr"
                         placeholder="Ex: ***********/24" value="{{ ip_input }}" required>
                </div>
                <div class="form-text">
                  <i class="bi bi-info-circle me-1"></i>
                  Utilisez la notation CIDR (ex: ***********/24 pour 254 adresses)
                </div>
              </div>

              <div class="col-lg-3 col-md-4 mb-3">
                <button type="submit" class="btn btn-success btn-lg w-100">
                  <i class="bi bi-play-circle me-2"></i>Lancer le Scan
                </button>
              </div>

              <div class="col-lg-3 col-md-12 mb-3">
                <a href="{{ url_for('historique') }}" class="btn btn-outline-primary btn-lg w-100">
                  <i class="bi bi-clock-history me-2"></i>Historique
                </a>
              </div>
            </div>

            <!-- Exemples de réseaux -->
            <div class="mt-3">
              <small class="text-muted fw-semibold">Exemples courants :</small>
              <div class="d-flex flex-wrap gap-2 mt-2">
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="***********/24">
                  ***********/24 <small>(Réseau domestique)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="172.16.0.0/24">
                  172.16.0.0/24 <small>(Réseau entreprise)</small>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm example-network" data-network="10.0.0.0/24">
                  10.0.0.0/24 <small>(Réseau privé)</small>
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  {% if error %}
  <div class="alert alert-danger">{{ error }}</div>
  {% endif %}

  {% if alerts %}
  <div class="alert alert-warning">
    <h5>⚠️ Alertes :</h5>
    <ul>
      {% for alert in alerts %}
      <li>{{ alert }}</li>
      {% endfor %}
    </ul>
  </div>
  {% endif %}

  {% if results %}
  <!-- Info sur les résultats -->
  <div class="alert alert-info mb-3">
    <i class="bi bi-info-circle me-2"></i>
    <strong>{{ results|length }} machines détectées</strong> sur le réseau {{ ip_input }}.
    Les machines sans SNMP configuré apparaissent comme "Machine détectée".
    <br><small class="text-muted">
      💡 Pour obtenir plus d'informations (nom, CPU, mémoire), configurez SNMP sur vos machines.
    </small>
  </div>

  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button id="filter-all" data-status="all" class="btn btn-outline-primary btn-sm btn-filter active">Tous</button>
      <button id="filter-up" data-status="up" class="btn btn-outline-success btn-sm btn-filter">En ligne</button>
      <button id="filter-down" data-status="down" class="btn btn-outline-danger btn-sm btn-filter">Hors ligne</button>
    </div>
    <div>
      <label>Afficher
        <select id="rowsCount" class="form-select form-select-sm d-inline-block w-auto ms-2">
          <option>5</option>
          <option selected>10</option>
          <option>25</option>
          <option>50</option>
          <option>100</option>
        </select>
        lignes
      </label>
    </div>
  </div>

  <table id="resultsTable" class="table table-bordered table-striped table-hover">
    <thead class="table-dark">
      <tr>
        <th>Adresse IP</th>
        <th>Nom (SNMP)</th>
        <th>État</th>
        <th>CPU (%)</th>
        <th>Mémoire (%)</th>
        <th>Disque (%)</th>
        <th>Uptime</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {% for machine in results %}
      <tr data-status="{{ machine['status'] if machine['status'] else machine.status }}">
        <td><a href="{{ url_for('machine_detail', ip=machine['ip'] if machine['ip'] else machine.ip) }}">{{ machine['ip'] if machine['ip'] else machine.ip }}</a></td>
        <td>
          {% set name = machine['name'] if machine['name'] else machine.name %}
          {% if name and name != 'N/A' %}
            {{ name }}
          {% else %}
            <span class="text-muted">
              <i class="bi bi-question-circle" title="Machine détectée sans SNMP"></i>
              Machine détectée
            </span>
          {% endif %}
        </td>
        <td>
          {% set status = machine['status'] if machine['status'] else machine.status %}
          {% if status == "up" %}
          <span class="badge bg-success">En ligne</span>
          {% else %}
          <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
        <td>
          {% set cpu = machine['cpu_load'] if machine['cpu_load'] else machine.cpu_load %}
          {% if cpu is not none %}
            <span class="badge {% if cpu > 80 %}bg-danger{% elif cpu > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ cpu }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set memory = machine['memory_usage'] if machine['memory_usage'] else machine.memory_usage %}
          {% if memory is not none %}
            <span class="badge {% if memory > 80 %}bg-danger{% elif memory > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ memory }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set disk = machine['disk_usage'] if machine['disk_usage'] else machine.disk_usage %}
          {% if disk is not none %}
            <span class="badge {% if disk > 80 %}bg-danger{% elif disk > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ disk }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set uptime = machine['uptime'] if machine['uptime'] else machine.uptime %}
          {% if uptime is not none %}
            {% set days = (uptime // 86400) %}
            {% set hours = ((uptime % 86400) // 3600) %}
            {% set minutes = ((uptime % 3600) // 60) %}
            <small>{{ days }}d {{ hours }}h {{ minutes }}m</small>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set desc = machine['description'] if machine['description'] else machine.description %}
          <small class="text-muted">{{ desc[:50] + '...' if desc and desc|length > 50 else desc if desc else 'N/A' }}</small>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% endif %}
</div>

<!-- CSS et JS DataTables + jQuery -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
  $(document).ready(function() {
    var table = $('#resultsTable').DataTable({
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
      },
      pageLength: 10,
      lengthChange: false,
      dom: 'lrtip'  // Pas de recherche native, on fait filtres custom
    });

    $('#rowsCount').on('change', function() {
      table.page.len($(this).val()).draw();
    });

    // Filtre personnalisé DataTables selon le bouton actif
    $.fn.dataTable.ext.search.push(
      function(settings, data, dataIndex) {
        var selectedStatus = $('.btn-filter.active').data('status');
        var status = $('#resultsTable tbody tr').eq(dataIndex).data('status');
        if (selectedStatus === 'all') {
          return true; // Tout afficher
        }
        return status === selectedStatus;
      }
    );

    function updateFilter(btn) {
      $('.btn-filter').removeClass('active');
      $(btn).addClass('active');
      table.draw();
    }

    $('#filter-all').on('click', function() {
      updateFilter(this);
    });
    $('#filter-up').on('click', function() {
      updateFilter(this);
    });
    $('#filter-down').on('click', function() {
      updateFilter(this);
    });
  });
</script>
{% endblock %}
